#!/usr/bin/env python3
"""
Test đầy đủ BlueStacks automation với Chrome và 13win
"""

from bluestacks_automation import BlueStacksAutomation
import time

def test_full_automation():
    """Test automation đầy đủ"""
    print("🧪 Test BlueStacks Full Automation")
    print("=" * 50)
    
    # Tạo automation instance
    automation = BlueStacksAutomation()
    
    print("📋 Bắt đầu test automation đầy đủ...")
    print()
    
    # Test 1: Khởi động BlueStacks
    print("🔍 Test 1: Khởi động BlueStacks...")
    if not automation.is_bluestacks_running():
        if automation.start_bluestacks():
            print("✅ BlueStacks đã khởi động")
            time.sleep(10)  # Wait for startup
        else:
            print("❌ Không thể khởi động BlueStacks")
            return False
    else:
        print("✅ BlueStacks đã chạy sẵn")
    
    # Test 2: Kết nối ADB
    print("\n🔍 Test 2: Kết nối ADB...")
    if automation.connect_adb():
        print("✅ ADB kết nối thành công")
    else:
        print("❌ ADB kết nối thất bại")
        return False
    
    # Test 3: Mở Chrome
    print("\n🔍 Test 3: Mở Chrome trong BlueStacks...")
    if automation.launch_chrome():
        print("✅ Chrome đã được mở")
        time.sleep(5)
    else:
        print("⚠️ Không thể mở Chrome tự động")
        print("💡 Bạn có thể mở Chrome thủ công trong BlueStacks")
    
    # Test 4: Truy cập 13win
    print("\n🔍 Test 4: Truy cập trang 13win...")
    if automation.navigate_to_13win():
        print("✅ Đã truy cập trang 13win")
        time.sleep(5)
    else:
        print("⚠️ Không thể truy cập trang tự động")
        print("💡 Bạn có thể mở trang thủ công")
    
    # Test 5: Simulation test (không đăng nhập thật)
    print("\n🔍 Test 5: Test simulation (không đăng nhập thật)...")
    print("📱 Thử tap vào tọa độ test...")
    
    # Test tap at center of screen
    automation._tap_screen(500, 500)
    time.sleep(2)
    
    print("✅ Test tap hoàn thành")
    
    print("\n🎉 TEST AUTOMATION HOÀN THÀNH!")
    print("=" * 50)
    print("📋 KẾT QUẢ:")
    print("✅ BlueStacks: Hoạt động")
    print("✅ ADB: Kết nối thành công")
    print("⚠️ Chrome/Navigation: Cần kiểm tra thủ công")
    print("✅ Input simulation: Hoạt động")
    print()
    print("💡 CÁCH SỬ DỤNG:")
    print("1. Chạy: python run_simple.py")
    print("2. Tích chọn: '📱 Tự động mở BlueStacks'")
    print("3. Bấm: '🚀 Bắt đầu đăng ký tự động'")
    print("4. Tool sẽ tự động làm tất cả!")
    print("=" * 50)

def test_with_demo_account():
    """Test với tài khoản demo"""
    print("\n🧪 Test với tài khoản demo")
    print("=" * 30)
    
    automation = BlueStacksAutomation()
    
    # Test quy trình hoàn chỉnh với tài khoản demo
    demo_username = "testuser123"
    demo_password = "testpass123"
    
    print(f"📝 Test với tài khoản demo:")
    print(f"   👤 Username: {demo_username}")
    print(f"   🔑 Password: {demo_password}")
    
    # Chỉ test các bước cơ bản, không thực sự đăng nhập
    if automation.is_bluestacks_running() and automation.connect_adb():
        print("✅ Sẵn sàng cho automation thực tế")
        print("💡 Có thể sử dụng với tool chính")
        return True
    else:
        print("❌ Chưa sẵn sàng cho automation")
        return False

if __name__ == "__main__":
    test_full_automation()
    test_with_demo_account()
