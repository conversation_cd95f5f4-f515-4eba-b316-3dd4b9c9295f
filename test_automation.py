#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST AUTOMATION - <PERSON><PERSON><PERSON> tra hệ thống trước khi chạy automation thật

Script này sẽ:
1. <PERSON><PERSON><PERSON> tra BlueStacks có chạy không
2. Test ADB connection
3. Test tap functionality
4. Hi<PERSON><PERSON> thị thông tin hệ thống
"""

import time
from bluestacks_automation import BlueStacksAutomation

def test_system():
    """Test toàn bộ hệ thống"""
    print("🧪 KIỂM TRA HỆ THỐNG AUTOMATION")
    print("=" * 50)
    
    automation = BlueStacksAutomation()
    
    # Test 1: BlueStacks Detection
    print("\n📋 Test 1: Phát hiện BlueStacks")
    print("-" * 30)
    
    if automation.bluestacks_path:
        print(f"✅ BlueStacks path: {automation.bluestacks_path}")
    else:
        print("❌ Không tìm thấy BlueStacks!")
        return False
    
    if automation.adb_path:
        print(f"✅ ADB path: {automation.adb_path}")
    else:
        print("❌ Không tìm thấy ADB!")
        return False
    
    # Test 2: BlueStacks Running
    print("\n📋 Test 2: Kiểm tra BlueStacks đang chạy")
    print("-" * 30)
    
    if automation.is_bluestacks_running():
        print("✅ BlueStacks đang chạy")
    else:
        print("❌ BlueStacks không chạy!")
        print("💡 Vui lòng mở BlueStacks trước khi test")
        return False
    
    # Test 3: ADB Connection
    print("\n📋 Test 3: Kết nối ADB")
    print("-" * 30)
    
    try:
        import subprocess
        result = subprocess.run([
            automation.adb_path, "devices"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"ADB devices output:")
        print(result.stdout)
        
        if "device" in result.stdout:
            print("✅ ADB có device kết nối")
        else:
            print("⚠️ ADB chưa có device, sẽ thử kết nối...")
            
            # Try connect
            connect_result = automation.connect_adb()
            if connect_result:
                print("✅ Kết nối ADB thành công")
            else:
                print("❌ Kết nối ADB thất bại")
                
    except Exception as e:
        print(f"❌ Lỗi test ADB: {str(e)}")
    
    # Test 4: Screen Size
    print("\n📋 Test 4: Kích thước màn hình")
    print("-" * 30)
    
    screen_size = automation.get_screen_size()
    if screen_size:
        print(f"✅ Screen size: {screen_size[0]}x{screen_size[1]}")
    else:
        print("⚠️ Không lấy được kích thước màn hình")
        print("💡 Có thể ADB chưa kết nối đúng")
    
    # Test 5: Tap Test (Optional)
    print("\n📋 Test 5: Test tap functionality")
    print("-" * 30)
    
    test_tap = input("❓ Bạn có muốn test tap (sẽ tap vào giữa màn hình)? (y/n): ").strip().lower()
    
    if test_tap in ['y', 'yes', 'có']:
        print("⏳ Sẽ tap vào giữa màn hình sau 3 giây...")
        print("💡 Hãy quan sát BlueStacks để xem có tap được không")
        
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        if screen_size:
            center_x = screen_size[0] // 2
            center_y = screen_size[1] // 2
            automation._tap_screen(center_x, center_y)
            print(f"✅ Đã tap tại ({center_x}, {center_y})")
        else:
            # Use default coordinates
            automation._tap_screen(400, 400)
            print("✅ Đã tap tại (400, 400) - tọa độ mặc định")
        
        print("❓ Bạn có thấy tap hoạt động trong BlueStacks không?")
    
    # Test 6: App Position Test
    print("\n📋 Test 6: Test vị trí app 13win")
    print("-" * 30)
    
    test_app = input("❓ Bạn có muốn test click vào app 13win? (y/n): ").strip().lower()
    
    if test_app in ['y', 'yes', 'có']:
        print("⏳ Sẽ click vào app 13win sau 3 giây...")
        print("💡 Hãy quan sát BlueStacks để xem có click đúng app không")
        
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        # Click on 13win app position
        automation._tap_screen(411, 215)
        print("✅ Đã click vào vị trí app 13win (411, 215)")
        
        print("❓ App 13win có mở được không?")
    
    print("\n🎉 HOÀN THÀNH TEST HỆ THỐNG!")
    print("=" * 50)
    
    return True

def main():
    """Hàm chính"""
    try:
        success = test_system()
        
        if success:
            print("\n✅ Hệ thống sẵn sàng cho automation!")
            print("💡 Bạn có thể chạy automation thật bằng:")
            print("   - python run_13win_automation.py")
            print("   - hoặc double-click run_automation.bat")
        else:
            print("\n❌ Hệ thống chưa sẵn sàng!")
            print("💡 Vui lòng khắc phục các vấn đề trên trước khi chạy automation")
        
        input("\nNhấn Enter để thoát...")
        
    except KeyboardInterrupt:
        print("\n\n👋 Test bị dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi test: {str(e)}")

if __name__ == "__main__":
    main()
