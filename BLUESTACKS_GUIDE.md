# 📱 HƯỚNG DẪN BLUESTACKS AUTOMATION

## 🎯 Tính năng mới
Tool run_simple đã được cập nhật với tính năng tự động BlueStacks:
- ✅ Tự động khởi động BlueStacks sau khi đăng ký thành công
- ✅ Mở Chrome trong BlueStacks
- ✅ Truy cập trang 13win
- ✅ Đăng nhập tài khoản vừa tạo
- ✅ Tự động bấm nút nhận 34k

## 🔧 Cài đặt BlueStacks

### 1. Tải và cài đặt BlueStacks
```
1. Truy cập: https://www.bluestacks.com/
2. Tải BlueStacks 5 (phiên bản mới nhất)
3. Cài đặt với cài đặt mặc định
4. Khởi động BlueStacks lần đầu
```

### 2. Cấu hình BlueStacks
```
1. Mở BlueStacks
2. <PERSON><PERSON><PERSON> thà<PERSON> setup ban đầu
3. <PERSON>ài đặt Chrome browser từ Play Store
4. <PERSON><PERSON><PERSON> b<PERSON>o BlueStacks chạy ổn định
```

### 2.1. Chọn Instance BlueStacks
BlueStacks hỗ trợ nhiều instance (máy ảo Android):
- **Pie64** (Android 9, 64-bit) - **Được khuyến nghị**
- **Nougat64** (Android 7, 64-bit)
- **Nougat32** (Android 7, 32-bit)
- **Android11** (Android 11, 64-bit)

**Cách kiểm tra instance hiện tại**:
```
1. Mở BlueStacks Multi-Instance Manager
2. Xem danh sách instances
3. Note tên instance đang sử dụng
4. Cập nhật trong config.py nếu cần
```

### 3. Bật ADB Debugging (Quan trọng!)
```
1. Trong BlueStacks, mở Settings
2. Tìm "About" hoặc "About Phone"
3. Tap 7 lần vào "Build Number" để bật Developer Options
4. Quay lại Settings → Developer Options
5. Bật "USB Debugging" hoặc "ADB Debugging"
```

## 🚀 Sử dụng tính năng

### 1. Khởi động tool
```bash
python run_simple.py
```

### 2. Cấu hình
- ✅ Nhập thông tin đăng ký như bình thường
- ✅ **Tích chọn "📱 Tự động mở BlueStacks và đăng nhập"**
- ✅ Bấm "🚀 Bắt đầu đăng ký tự động"

### 3. Quy trình tự động
```
1. Tool đăng ký tài khoản trên Chrome (PC)
2. Sau khi thành công → Tự động khởi động BlueStacks
3. Mở Chrome trong BlueStacks
4. Truy cập trang 13win
5. Đăng nhập tài khoản vừa tạo
6. Tự động bấm nút nhận 34k
```

## ⚙️ Cấu hình nâng cao

### File config.py
```python
BLUESTACKS_CONFIG = {
    # Đường dẫn BlueStacks (tự động tìm nếu None)
    'bluestacks_path': None,

    # Tên instance BlueStacks
    'instance_name': 'Pie64',  # Có thể là: Pie64, Nougat64, Android11, etc.

    # Cổng ADB (tự động phát hiện theo instance)
    'adb_port': 5556,  # Pie64=5556, Nougat64=5555, Nougat32=5557

    # Timeout (giây)
    'startup_timeout': 30,
    'app_launch_timeout': 15,
    'login_timeout': 20,

    # Tự động click nút thưởng
    'auto_click_reward': True,
}
```

## 🔍 Xử lý sự cố

### ❌ "BlueStacks automation không khả dụng"
**Nguyên nhân**: Thiếu thư viện psutil
**Giải pháp**:
```bash
pip install psutil
```

### ❌ "Không tìm thấy BlueStacks"
**Nguyên nhân**: BlueStacks chưa cài đặt hoặc đường dẫn sai
**Giải pháp**:
1. Cài đặt BlueStacks từ trang chính thức
2. Hoặc cấu hình đường dẫn trong config.py:
```python
'bluestacks_path': 'C:\\Program Files\\BlueStacks_nxt\\HD-Player.exe'
```

### ❌ "Không thể kết nối ADB"
**Nguyên nhân**: ADB debugging chưa bật
**Giải pháp**:
1. Bật Developer Options trong BlueStacks
2. Bật USB/ADB Debugging
3. Khởi động lại BlueStacks

### ❌ "Không thể mở Chrome"
**Nguyên nhân**: Chrome chưa cài đặt trong BlueStacks
**Giải pháp**:
1. Mở BlueStacks
2. Vào Play Store
3. Tìm và cài đặt Chrome
4. Đảm bảo Chrome hoạt động bình thường

### ❌ "Đăng nhập thất bại"
**Nguyên nhân**: Tọa độ click không chính xác
**Giải pháp**:
1. Kiểm tra kích thước màn hình BlueStacks
2. Điều chỉnh tọa độ trong bluestacks_automation.py
3. Hoặc thực hiện đăng nhập thủ công

## 📋 Kiểm tra hệ thống

### Test BlueStacks automation
```bash
python bluestacks_automation.py
```

### Kiểm tra ADB
```bash
# Trong Command Prompt
adb devices
# Hoặc
"C:\Program Files\BlueStacks_nxt\HD-Adb.exe" devices
```

## 🎯 Lưu ý quan trọng

### ✅ Điều kiện hoạt động
- BlueStacks 5 đã cài đặt và cấu hình
- ADB debugging đã bật
- Chrome đã cài đặt trong BlueStacks
- Kết nối internet ổn định

### ⚠️ Hạn chế
- Tọa độ click có thể cần điều chỉnh theo màn hình
- Tốc độ internet ảnh hưởng đến thời gian load
- Một số thao tác có thể cần thực hiện thủ công

### 💡 Mẹo sử dụng
- Để BlueStacks mở sẵn để tăng tốc độ
- Kiểm tra log để theo dõi tiến trình
- Có thể tắt tính năng nếu không cần thiết
- Sử dụng kết hợp với tính năng nhận thưởng tự động

## 🔄 Quy trình hoàn chỉnh

```
1. Khởi động tool run_simple
   ↓
2. Tích chọn "📱 Tự động mở BlueStacks"
   ↓
3. Bấm "🚀 Bắt đầu đăng ký tự động"
   ↓
4. Tool đăng ký trên Chrome (PC)
   ↓
5. Tự động khởi động BlueStacks
   ↓
6. Mở Chrome trong BlueStacks
   ↓
7. Truy cập 13win và đăng nhập
   ↓
8. Tự động nhận thưởng 34k
   ↓
9. Hoàn thành!
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra log trong tool
2. Đọc phần xử lý sự cố ở trên
3. Thử chạy test BlueStacks automation
4. Kiểm tra cài đặt BlueStacks và ADB

---
**Lưu ý**: Tính năng này là bổ sung, tool vẫn hoạt động bình thường nếu không sử dụng BlueStacks.
