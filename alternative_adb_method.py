#!/usr/bin/env python3
"""
Phương pháp thay thế để bật ADB trong BlueStacks
"""

import subprocess
import os
import time

def try_bluestacks_external_settings():
    """Thử mở BlueStacks settings từ bên ngoài"""
    print("🔧 PHƯƠNG PHÁP THAY THẾ: BlueStacks External Settings")
    print("=" * 60)
    
    # Thử các cách mở BlueStacks settings
    methods = [
        {
            'name': 'BlueStacks Settings App',
            'path': r'C:\Program Files\BlueStacks_nxt\HD-ConfigHttpProxy.exe'
        },
        {
            'name': 'BlueStacks Multi-Instance Manager',
            'path': r'C:\Program Files\BlueStacks_nxt\HD-MultiInstanceManager.exe'
        },
        {
            'name': 'BlueStacks App Player Settings',
            'path': r'C:\Program Files\BlueStacks_nxt\HD-Player.exe --settings'
        }
    ]
    
    print("🔍 Thử các phương pháp mở settings:")
    
    for method in methods:
        print(f"\n📋 {method['name']}:")
        
        if 'HD-MultiInstanceManager.exe' in method['path']:
            path = r'C:\Program Files\BlueStacks_nxt\HD-MultiInstanceManager.exe'
            if os.path.exists(path):
                print(f"✅ Tìm thấy: {path}")
                print("💡 Hướng dẫn:")
                print("   1. Mở Multi-Instance Manager")
                print("   2. Tìm instance Pie64")
                print("   3. Click Settings (bánh răng) bên cạnh Pie64")
                print("   4. Tìm Advanced Settings")
                print("   5. Bật ADB debugging")
                
                try:
                    subprocess.Popen([path])
                    print("🚀 Đã mở Multi-Instance Manager!")
                    return True
                except Exception as e:
                    print(f"❌ Lỗi: {e}")
            else:
                print(f"❌ Không tìm thấy: {path}")
        
        elif os.path.exists(method['path'].split()[0]):
            print(f"✅ Tìm thấy: {method['path']}")
        else:
            print(f"❌ Không tìm thấy: {method['path']}")
    
    return False

def try_registry_method():
    """Thử bật ADB qua registry (nâng cao)"""
    print("\n🔧 PHƯƠNG PHÁP NÂNG CAO: Registry")
    print("=" * 40)
    print("⚠️ Cần quyền Administrator")
    
    # Registry keys for BlueStacks ADB
    registry_commands = [
        'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\BlueStacks_nxt" /v "AdbEnabled" /t REG_DWORD /d 1 /f',
        'reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\BlueStacks_nxt\\Guests\\Android" /v "AdbEnabled" /t REG_DWORD /d 1 /f'
    ]
    
    print("💡 Lệnh registry (chạy với quyền Admin):")
    for cmd in registry_commands:
        print(f"   {cmd}")
    
    print("\n⚠️ Lưu ý: Chỉ sử dụng nếu hiểu về registry!")

def try_config_file_method():
    """Thử sửa file config BlueStacks"""
    print("\n🔧 PHƯƠNG PHÁP CONFIG FILE")
    print("=" * 40)
    
    config_paths = [
        r'C:\ProgramData\BlueStacks_nxt\bluestacks.conf',
        r'C:\ProgramData\BlueStacks_nxt\UserData\bluestacks.conf',
        r'%LOCALAPPDATA%\BlueStacks_nxt\bluestacks.conf'
    ]
    
    print("🔍 Tìm file config BlueStacks:")
    
    for path in config_paths:
        expanded_path = os.path.expandvars(path)
        if os.path.exists(expanded_path):
            print(f"✅ Tìm thấy: {expanded_path}")
            print("💡 Có thể thêm dòng: bst.feature.adb_enabled=1")
        else:
            print(f"❌ Không có: {expanded_path}")

def show_manual_steps():
    """Hiển thị các bước thủ công"""
    print("\n🔧 CÁC BƯỚC THỦ CÔNG KHÁC")
    print("=" * 40)
    
    print("📱 1. Thử trong Android Settings:")
    print("   - Vào System → About phone")
    print("   - Tìm Build number và tap 7 lần")
    print("   - Nếu không có Build number, thử tap 7 lần vào:")
    print("     * Android version")
    print("     * Kernel version")
    print("     * Model number")
    
    print("\n📱 2. Thử BlueStacks sidebar:")
    print("   - Nhìn bên phải màn hình BlueStacks")
    print("   - Tìm biểu tượng Settings hoặc hamburger menu")
    print("   - Vào Advanced hoặc Engine settings")
    
    print("\n📱 3. Thử restart BlueStacks:")
    print("   - Đóng BlueStacks hoàn toàn")
    print("   - Mở Task Manager, kill tất cả process BlueStacks")
    print("   - Khởi động lại BlueStacks")
    print("   - Thử lại các bước trên")

def test_current_adb():
    """Test ADB hiện tại"""
    print("\n🧪 TEST ADB HIỆN TẠI")
    print("=" * 30)
    
    try:
        result = subprocess.run([
            r'C:\Program Files\BlueStacks_nxt\HD-Adb.exe', 'devices'
        ], capture_output=True, text=True, timeout=10)
        
        print("ADB Output:")
        print(result.stdout)
        
        if 'device' in result.stdout and 'emulator' in result.stdout:
            print("🎉 ADB đã hoạt động!")
            return True
        else:
            print("❌ ADB chưa hoạt động")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test ADB: {e}")
        return False

def main():
    print("🚀 BlueStacks ADB - Phương pháp thay thế")
    print("=" * 50)
    
    # Test ADB hiện tại trước
    if test_current_adb():
        print("✅ ADB đã hoạt động, không cần làm gì thêm!")
        return
    
    # Thử các phương pháp khác
    try_bluestacks_external_settings()
    try_config_file_method()
    show_manual_steps()
    
    print("\n" + "=" * 50)
    print("💡 KHUYẾN NGHỊ:")
    print("1. Thử mở Multi-Instance Manager")
    print("2. Tìm settings của instance Pie64")
    print("3. Bật ADB trong Advanced settings")
    print("4. Nếu không được, thử các bước thủ công")
    print("=" * 50)

if __name__ == "__main__":
    main()
