"""
Configuration file for Chrome proxy tool
"""

# Proxy configuration
PROXY_CONFIG = {
    # HTTP/HTTPS proxy
    'http_proxy': None,  # Example: 'http://proxy.example.com:8080'
    'https_proxy': None,  # Example: 'https://proxy.example.com:8080'

    # SOCKS proxy
    'socks_proxy': None,  # Example: 'socks5://proxy.example.com:1080'

    # Proxy authentication (if required)
    'username': None,
    'password': None,
}

# Target website
TARGET_URL = "https://www.13win16.com/?id=391111507"

# Chrome options
CHROME_OPTIONS = {
    'headless': False,  # Set to True to run in headless mode
    'disable_images': False,  # Set to True to disable image loading for faster browsing
    'window_size': (1920, 1080),
    'user_agent': None,  # Custom user agent if needed
}

# BlueStacks configuration
BLUESTACKS_CONFIG = {
    # BlueStacks installation path (auto-detect if None)
    'bluestacks_path': None,  # Example: 'C:\\Program Files\\BlueStacks_nxt\\HD-Player.exe'

    # BlueStacks instance name (default instance if None)
    'instance_name': 'Pie64',  # BlueStacks instance name

    # ADB configuration
    'adb_path': None,  # Auto-detect if None
    'adb_port': 5554,  # Default ADB port for BlueStacks (emulator-5554)

    # App configuration
    'app_package': 'com.android.chrome',  # Chrome browser package
    'app_activity': 'com.google.android.apps.chrome.Main',

    # 13win website URL for mobile
    'mobile_url': 'https://13win16.com/?id=391111507',

    # Timeouts (in seconds)
    'startup_timeout': 30,
    'app_launch_timeout': 15,
    'login_timeout': 20,

    # Auto-click settings
    'auto_click_reward': True,  # Auto click 34k reward button
    'reward_button_text': '34',  # Text to look for in reward button
}

# Timeouts (in seconds)
TIMEOUTS = {
    'page_load': 30,
    'implicit_wait': 10,
}
