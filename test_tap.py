#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST TAP - Ki<PERSON><PERSON> tra tap có hoạt động không

Script này sẽ test tap trực tiếp để xem có hoạt động trong BlueStacks không
"""

import time
import subprocess
import sys

def test_direct_tap():
    """Test tap trực tiếp bằng ADB"""
    print("🧪 TEST TAP TRỰC TIẾP")
    print("=" * 40)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    # Check devices
    print("🔍 Kiểm tra devices...")
    result = subprocess.run([adb_path, "devices"], capture_output=True, text=True)
    print(f"Devices: {result.stdout}")
    
    if "emulator-5554" not in result.stdout:
        print("❌ Không tìm thấy emulator-5554!")
        return False
    
    print("✅ Tìm thấy emulator-5554")
    
    # Test screen size
    print("\n📱 Kiểm tra kích thước màn hình...")
    size_result = subprocess.run([
        adb_path, "-s", "emulator-5554", "shell", "wm", "size"
    ], capture_output=True, text=True)
    
    print(f"Screen size output: {size_result.stdout}")
    
    # Test tap at center
    print("\n👆 Test tap tại giữa màn hình...")
    print("⏳ Sẽ tap sau 3 giây, hãy quan sát BlueStacks...")
    
    for i in range(3, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    # Tap at center (assuming 1920x1080)
    center_x, center_y = 960, 540
    
    print(f"👆 Tap tại ({center_x}, {center_y})...")
    tap_result = subprocess.run([
        adb_path, "-s", "emulator-5554", 
        "shell", "input", "tap", str(center_x), str(center_y)
    ], capture_output=True, text=True)
    
    if tap_result.returncode == 0:
        print("✅ Lệnh tap thành công!")
    else:
        print(f"❌ Lệnh tap thất bại: {tap_result.stderr}")
    
    # Test tap at 13win app position
    print(f"\n👆 Test tap tại vị trí app 13win (411, 215)...")
    time.sleep(2)
    
    app_tap_result = subprocess.run([
        adb_path, "-s", "emulator-5554", 
        "shell", "input", "tap", "411", "215"
    ], capture_output=True, text=True)
    
    if app_tap_result.returncode == 0:
        print("✅ Lệnh tap app thành công!")
    else:
        print(f"❌ Lệnh tap app thất bại: {app_tap_result.stderr}")
    
    # Test multiple taps
    print(f"\n👆 Test multiple taps...")
    positions = [
        (400, 300),  # Login area
        (400, 400),  # Password area  
        (400, 500),  # Button area
    ]
    
    for i, (x, y) in enumerate(positions):
        print(f"   Tap {i+1}: ({x}, {y})")
        subprocess.run([
            adb_path, "-s", "emulator-5554", 
            "shell", "input", "tap", str(x), str(y)
        ], capture_output=True, text=True)
        time.sleep(1)
    
    print("\n🎉 Test tap hoàn thành!")
    print("❓ Bạn có thấy các tap hoạt động trong BlueStacks không?")
    
    return True

def test_input_text():
    """Test nhập text"""
    print("\n📝 TEST NHẬP TEXT")
    print("=" * 40)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    # First tap somewhere to focus
    print("👆 Tap để focus...")
    subprocess.run([
        adb_path, "-s", "emulator-5554", 
        "shell", "input", "tap", "400", "300"
    ], capture_output=True, text=True)
    
    time.sleep(2)
    
    # Input test text
    test_text = "testuser123"
    print(f"📝 Nhập text: {test_text}")
    
    text_result = subprocess.run([
        adb_path, "-s", "emulator-5554", 
        "shell", "input", "text", test_text
    ], capture_output=True, text=True)
    
    if text_result.returncode == 0:
        print("✅ Lệnh nhập text thành công!")
    else:
        print(f"❌ Lệnh nhập text thất bại: {text_result.stderr}")
    
    return True

def main():
    """Hàm chính"""
    try:
        print("🚀 BẮT ĐẦU TEST TAP VÀ INPUT")
        print("=" * 50)
        print("💡 Hãy quan sát BlueStacks trong quá trình test")
        print("💡 Đảm bảo BlueStacks đang mở và hiển thị")
        print()
        
        input("⏸️ Nhấn Enter để bắt đầu test...")
        
        # Test tap
        test_direct_tap()
        
        # Test input
        test_input_text()
        
        print("\n" + "=" * 50)
        print("🎉 HOÀN THÀNH TẤT CẢ TEST!")
        print("💡 Nếu bạn thấy tap và input hoạt động, automation sẽ work!")
        print("💡 Nếu không thấy gì, có thể cần điều chỉnh cấu hình BlueStacks")
        
        input("\nNhấn Enter để thoát...")
        
    except KeyboardInterrupt:
        print("\n\n👋 Test bị dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi test: {str(e)}")

if __name__ == "__main__":
    main()
