#!/usr/bin/env python3
"""
Test script để kiểm tra BlueStacks automation với instance Pie64
"""

import time
from bluestacks_automation import BlueStacksAutomation

def test_bluestacks_pie64():
    """Test BlueStacks automation với instance Pie64"""
    print("🧪 Testing BlueStacks Pie64 Automation...")
    print("=" * 50)
    
    # Khởi tạo automation
    automation = BlueStacksAutomation()
    
    print(f"📱 Instance: {automation.instance_name}")
    print(f"🔌 ADB Port: {automation.adb_port}")
    print(f"📂 BlueStacks Path: {automation.bluestacks_path}")
    print(f"🔧 ADB Path: {automation.adb_path}")
    print()
    
    # Kiểm tra trạng thái hiện tại
    if automation.is_bluestacks_running():
        print("✅ BlueStacks đang chạy")
    else:
        print("❌ BlueStacks chưa chạy")
        
        # Thử khởi động BlueStacks
        print("🚀 Thử khởi động BlueStacks...")
        if automation.start_bluestacks():
            print("✅ Khởi động BlueStacks thành công!")
        else:
            print("❌ Khởi động BlueStacks thất bại!")
            return
    
    print()
    print("⏳ Chờ 10 giây để BlueStacks khởi động hoàn toàn...")
    time.sleep(10)
    
    # Thử kết nối ADB
    print("🔗 Thử kết nối ADB...")
    if automation.connect_adb():
        print("✅ Kết nối ADB thành công!")
        
        # Lấy kích thước màn hình
        screen_size = automation.get_screen_size()
        if screen_size:
            print(f"📱 Kích thước màn hình: {screen_size[0]}x{screen_size[1]}")
        
    else:
        print("❌ Kết nối ADB thất bại!")
    
    print()
    print("🎯 Test hoàn thành!")
    print("💡 Nếu có lỗi, hãy kiểm tra:")
    print("   - BlueStacks đã cài đặt chưa")
    print("   - ADB debugging đã bật chưa")
    print("   - Instance name có đúng không")

if __name__ == "__main__":
    test_bluestacks_pie64()
