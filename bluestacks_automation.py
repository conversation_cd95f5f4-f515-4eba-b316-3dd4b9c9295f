#!/usr/bin/env python3
"""
BlueStacks Automation Module
Tự động khởi động BlueStacks, mở app và đăng nhập tà<PERSON> k<PERSON>n
"""

import os
import time
import subprocess
import logging
from pathlib import Path
import psutil
import re

try:
    from config import BLUESTACKS_CONFIG
except ImportError:
    # Default config if config.py not found
    BLUESTACKS_CONFIG = {
        'bluestacks_path': None,
        'instance_name': 'Nougat64',
        'adb_path': None,
        'adb_port': 5555,
        'app_package': 'com.android.chrome',
        'app_activity': 'com.google.android.apps.chrome.Main',
        'mobile_url': 'https://13win16.com/?id=391111507',
        'startup_timeout': 30,
        'app_launch_timeout': 15,
        'login_timeout': 20,
        'auto_click_reward': True,
        'reward_button_text': '34',
    }

logger = logging.getLogger(__name__)


class BlueStacksAutomation:
    """Class để tự động hóa BlueStacks"""

    def __init__(self, log_callback=None):
        """
        Initialize BlueStacks automation

        Args:
            log_callback: Function to call for logging (optional)
        """
        self.log_callback = log_callback or print
        self.bluestacks_path = None
        self.adb_path = None
        self.adb_port = BLUESTACKS_CONFIG['adb_port']
        self.instance_name = BLUESTACKS_CONFIG['instance_name']

        # Auto-detect paths
        self._detect_bluestacks_path()
        self._detect_adb_path()

    def log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _detect_bluestacks_path(self):
        """Tự động tìm đường dẫn BlueStacks"""
        if BLUESTACKS_CONFIG['bluestacks_path']:
            self.bluestacks_path = BLUESTACKS_CONFIG['bluestacks_path']
            return

        # Common BlueStacks installation paths
        possible_paths = [
            r"C:\Program Files\BlueStacks_nxt\HD-Player.exe",
            r"C:\Program Files (x86)\BlueStacks_nxt\HD-Player.exe",
            r"C:\Program Files\BlueStacks\HD-Player.exe",
            r"C:\Program Files (x86)\BlueStacks\HD-Player.exe",
            r"C:\ProgramData\BlueStacks_nxt\HD-Player.exe",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                self.bluestacks_path = path
                self.log(f"🔍 Tìm thấy BlueStacks: {path}")
                return

        self.log("⚠️ Không tìm thấy BlueStacks, vui lòng cài đặt hoặc cấu hình đường dẫn")

    def _detect_adb_path(self):
        """Tự động tìm đường dẫn ADB"""
        if BLUESTACKS_CONFIG['adb_path']:
            self.adb_path = BLUESTACKS_CONFIG['adb_path']
            return

        # Common ADB paths
        possible_paths = [
            r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe",
            r"C:\Program Files (x86)\BlueStacks_nxt\HD-Adb.exe",
            r"C:\Program Files\BlueStacks\HD-Adb.exe",
            r"C:\Program Files (x86)\BlueStacks\HD-Adb.exe",
            "adb.exe",  # If in PATH
        ]

        for path in possible_paths:
            if os.path.exists(path) or path == "adb.exe":
                try:
                    # Test if adb works
                    result = subprocess.run([path, "version"],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.adb_path = path
                        self.log(f"🔍 Tìm thấy ADB: {path}")
                        return
                except:
                    continue

        self.log("⚠️ Không tìm thấy ADB")

    def _detect_adb_port(self):
        """Tự động phát hiện ADB port cho instance"""
        try:
            # Common ports for different instances
            instance_ports = {
                'Nougat64': 5555,
                'Pie64': 5556,
                'Nougat32': 5557,
                'Pie64_2': 5558,
                'Android11': 5559,
            }

            if self.instance_name in instance_ports:
                self.adb_port = instance_ports[self.instance_name]
                self.log(f"🔍 Sử dụng port {self.adb_port} cho instance {self.instance_name}")
            else:
                # Try to detect from running processes or config
                self.log(f"⚠️ Không biết port cho instance {self.instance_name}, sử dụng port mặc định {self.adb_port}")

        except Exception as e:
            self.log(f"⚠️ Lỗi phát hiện ADB port: {str(e)}")

    def is_bluestacks_running(self):
        """Kiểm tra BlueStacks có đang chạy không"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'bluestacks' in proc.info['name'].lower() or 'hd-player' in proc.info['name'].lower():
                    return True
            return False
        except:
            return False

    def start_bluestacks(self):
        """Khởi động BlueStacks với instance cụ thể"""
        try:
            if not self.bluestacks_path:
                self.log("❌ Không tìm thấy đường dẫn BlueStacks")
                return False

            if self.is_bluestacks_running():
                self.log("✅ BlueStacks đã đang chạy")
                return True

            self.log(f"🚀 Đang khởi động BlueStacks với instance: {self.instance_name}")

            # Start BlueStacks with specific instance
            cmd = [self.bluestacks_path]
            if self.instance_name:
                cmd.extend(["--instance", self.instance_name])

            subprocess.Popen(cmd, shell=True)

            # Wait for BlueStacks to start
            timeout = BLUESTACKS_CONFIG['startup_timeout']
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self.is_bluestacks_running():
                    self.log("✅ BlueStacks đã khởi động thành công")
                    time.sleep(5)  # Wait a bit more for full startup
                    return True
                time.sleep(2)

            self.log("❌ Timeout khởi động BlueStacks")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi khởi động BlueStacks: {str(e)}")
            return False

    def connect_adb(self):
        """Kết nối ADB với BlueStacks"""
        try:
            if not self.adb_path:
                self.log("❌ Không tìm thấy ADB")
                return False

            # Auto-detect ADB port for the instance
            self._detect_adb_port()

            # Connect to BlueStacks
            self.log(f"🔗 Kết nối ADB với BlueStacks instance {self.instance_name} (port {self.adb_port})...")

            result = subprocess.run([
                self.adb_path, "connect", f"127.0.0.1:{self.adb_port}"
            ], capture_output=True, text=True, timeout=10)

            if "connected" in result.stdout.lower():
                self.log("✅ Kết nối ADB thành công")
                return True
            else:
                self.log(f"⚠️ Kết nối ADB: {result.stdout}")
                return True  # Sometimes it still works

        except Exception as e:
            self.log(f"❌ Lỗi kết nối ADB: {str(e)}")
            return False

    def launch_chrome(self):
        """Mở Chrome browser trong BlueStacks"""
        try:
            if not self.adb_path:
                return False

            self.log("🌐 Đang mở Chrome trong BlueStacks...")

            # Launch Chrome
            result = subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "am", "start", "-n",
                f"{BLUESTACKS_CONFIG['app_package']}/{BLUESTACKS_CONFIG['app_activity']}"
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                self.log("✅ Chrome đã được mở")
                time.sleep(3)
                return True
            else:
                self.log(f"⚠️ Lỗi mở Chrome: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi mở Chrome: {str(e)}")
            return False

    def navigate_to_13win(self):
        """Điều hướng đến trang 13win"""
        try:
            if not self.adb_path:
                return False

            url = BLUESTACKS_CONFIG['mobile_url']
            self.log(f"🌐 Truy cập {url}...")

            # Open URL in Chrome
            result = subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "am", "start", "-a", "android.intent.action.VIEW",
                "-d", url
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                self.log("✅ Đã truy cập trang 13win")
                time.sleep(5)
                return True
            else:
                self.log(f"⚠️ Lỗi truy cập: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi truy cập trang: {str(e)}")
            return False

    def simulate_login(self, username, password):
        """Mô phỏng đăng nhập tài khoản (sử dụng input events)"""
        try:
            if not self.adb_path:
                return False

            self.log(f"🔐 Đăng nhập tài khoản: {username}")

            # Wait for page to load
            time.sleep(5)

            # Simulate tap on login area (coordinates may need adjustment)
            # These are example coordinates - you may need to adjust based on your screen
            self.log("👆 Tìm và click vào form đăng nhập...")

            # Tap on username field (example coordinates)
            self._tap_screen(500, 400)
            time.sleep(1)

            # Clear and input username
            self._input_text(username)
            time.sleep(1)

            # Tap on password field
            self._tap_screen(500, 500)
            time.sleep(1)

            # Input password
            self._input_text(password)
            time.sleep(1)

            # Tap login button
            self._tap_screen(500, 600)
            time.sleep(3)

            self.log("✅ Đã thực hiện đăng nhập")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập: {str(e)}")
            return False

    def _tap_screen(self, x, y):
        """Tap vào tọa độ trên màn hình"""
        try:
            subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "input", "tap", str(x), str(y)
            ], capture_output=True, timeout=5)
        except:
            pass

    def _input_text(self, text):
        """Nhập text"""
        try:
            # Clear current text first
            subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "input", "keyevent", "KEYCODE_CTRL_A"
            ], capture_output=True, timeout=5)

            # Input new text
            subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "input", "text", text
            ], capture_output=True, timeout=5)
        except:
            pass

    def click_reward_button(self):
        """Tìm và click nút nhận thưởng 34k"""
        try:
            if not self.adb_path:
                return False

            self.log("🎁 Tìm kiếm nút nhận thưởng 34k...")

            # Wait for page to load
            time.sleep(5)

            # Try to find and click reward button
            # This is a simplified approach - you may need to use UI Automator
            # or other methods for more precise element detection

            # Example coordinates for reward button (may need adjustment)
            reward_coordinates = [
                (400, 700),  # Common position 1
                (500, 750),  # Common position 2
                (600, 800),  # Common position 3
            ]

            for x, y in reward_coordinates:
                self.log(f"👆 Thử click tại tọa độ ({x}, {y})...")
                self._tap_screen(x, y)
                time.sleep(2)

            self.log("✅ Đã thực hiện click nút nhận thưởng")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi click nút thưởng: {str(e)}")
            return False

    def auto_process_account(self, username, password):
        """
        Quy trình tự động hoàn chỉnh:
        1. Khởi động BlueStacks
        2. Mở Chrome
        3. Truy cập 13win
        4. Đăng nhập
        5. Nhận thưởng
        """
        try:
            self.log("🤖 Bắt đầu quy trình tự động BlueStacks...")

            # Step 1: Start BlueStacks
            if not self.start_bluestacks():
                return False

            # Step 2: Connect ADB
            if not self.connect_adb():
                return False

            # Step 3: Launch Chrome
            if not self.launch_chrome():
                return False

            # Step 4: Navigate to 13win
            if not self.navigate_to_13win():
                return False

            # Step 5: Login
            if not self.simulate_login(username, password):
                self.log("⚠️ Đăng nhập có thể cần thực hiện thủ công")

            # Step 6: Click reward button
            if BLUESTACKS_CONFIG['auto_click_reward']:
                time.sleep(5)  # Wait for login to complete
                if not self.click_reward_button():
                    self.log("⚠️ Nhận thưởng có thể cần thực hiện thủ công")

            self.log("🎉 Hoàn thành quy trình BlueStacks!")
            self.log("💡 Vui lòng kiểm tra BlueStacks để xác nhận kết quả")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi quy trình tự động: {str(e)}")
            return False

    def get_screen_size(self):
        """Lấy kích thước màn hình BlueStacks"""
        try:
            if not self.adb_path:
                return None

            result = subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "wm", "size"
            ], capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                # Parse output like "Physical size: 1920x1080"
                match = re.search(r'(\d+)x(\d+)', result.stdout)
                if match:
                    width, height = int(match.group(1)), int(match.group(2))
                    self.log(f"📱 Kích thước màn hình: {width}x{height}")
                    return (width, height)

            return None

        except Exception as e:
            self.log(f"❌ Lỗi lấy kích thước màn hình: {str(e)}")
            return None


def test_bluestacks_automation():
    """Test function for BlueStacks automation"""
    print("🧪 Testing BlueStacks Automation...")

    automation = BlueStacksAutomation()

    # Test detection
    print(f"BlueStacks path: {automation.bluestacks_path}")
    print(f"ADB path: {automation.adb_path}")

    # Test if BlueStacks is running
    if automation.is_bluestacks_running():
        print("✅ BlueStacks is running")
    else:
        print("❌ BlueStacks is not running")


if __name__ == "__main__":
    test_bluestacks_automation()
