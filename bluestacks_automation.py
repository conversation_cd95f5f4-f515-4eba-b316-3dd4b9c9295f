#!/usr/bin/env python3
"""
BlueStacks Automation Module
Tự động khởi động BlueStacks, mở app và đăng nhập tà<PERSON> k<PERSON>n
"""

import os
import time
import subprocess
import logging
from pathlib import Path
import psutil
import re

try:
    from config import BLUESTACKS_CONFIG
except ImportError:
    # Default config if config.py not found
    BLUESTACKS_CONFIG = {
        'bluestacks_path': None,
        'instance_name': 'Nougat64',
        'adb_path': None,
        'adb_port': 5555,
        'app_package': 'com.android.chrome',
        'app_activity': 'com.google.android.apps.chrome.Main',
        'mobile_url': 'https://13win16.com/?id=391111507',
        'startup_timeout': 30,
        'app_launch_timeout': 15,
        'login_timeout': 20,
        'auto_click_reward': True,
        'reward_button_text': '34',
    }

logger = logging.getLogger(__name__)


class BlueStacksAutomation:
    """Class để tự động hóa BlueStacks"""

    def __init__(self, log_callback=None):
        """
        Initialize BlueStacks automation

        Args:
            log_callback: Function to call for logging (optional)
        """
        self.log_callback = log_callback or print
        self.bluestacks_path = None
        self.adb_path = None
        self.adb_port = BLUESTACKS_CONFIG['adb_port']
        self.instance_name = BLUESTACKS_CONFIG['instance_name']

        # Auto-detect paths
        self._detect_bluestacks_path()
        self._detect_adb_path()

    def log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _detect_bluestacks_path(self):
        """Tự động tìm đường dẫn BlueStacks"""
        if BLUESTACKS_CONFIG['bluestacks_path']:
            self.bluestacks_path = BLUESTACKS_CONFIG['bluestacks_path']
            return

        # Common BlueStacks installation paths
        possible_paths = [
            r"C:\Program Files\BlueStacks_nxt\HD-Player.exe",
            r"C:\Program Files (x86)\BlueStacks_nxt\HD-Player.exe",
            r"C:\Program Files\BlueStacks\HD-Player.exe",
            r"C:\Program Files (x86)\BlueStacks\HD-Player.exe",
            r"C:\ProgramData\BlueStacks_nxt\HD-Player.exe",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                self.bluestacks_path = path
                self.log(f"🔍 Tìm thấy BlueStacks: {path}")
                return

        self.log("⚠️ Không tìm thấy BlueStacks, vui lòng cài đặt hoặc cấu hình đường dẫn")

    def _detect_adb_path(self):
        """Tự động tìm đường dẫn ADB"""
        if BLUESTACKS_CONFIG['adb_path']:
            self.adb_path = BLUESTACKS_CONFIG['adb_path']
            return

        # Common ADB paths
        possible_paths = [
            r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe",
            r"C:\Program Files (x86)\BlueStacks_nxt\HD-Adb.exe",
            r"C:\Program Files\BlueStacks\HD-Adb.exe",
            r"C:\Program Files (x86)\BlueStacks\HD-Adb.exe",
            "adb.exe",  # If in PATH
        ]

        for path in possible_paths:
            if os.path.exists(path) or path == "adb.exe":
                try:
                    # Test if adb works
                    result = subprocess.run([path, "version"],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.adb_path = path
                        self.log(f"🔍 Tìm thấy ADB: {path}")
                        return
                except:
                    continue

        self.log("⚠️ Không tìm thấy ADB")

    def _detect_adb_port(self):
        """Tự động phát hiện ADB port cho instance"""
        try:
            # Common ports for different instances
            instance_ports = {
                'Nougat64': 5555,
                'Pie64': 5554,  # Thực tế BlueStacks sử dụng 5554
                'Nougat32': 5557,
                'Pie64_2': 5558,
                'Android11': 5559,
            }

            if self.instance_name in instance_ports:
                self.adb_port = instance_ports[self.instance_name]
                self.log(f"🔍 Sử dụng port {self.adb_port} cho instance {self.instance_name}")
            else:
                # Try to detect from running processes or config
                self.log(f"⚠️ Không biết port cho instance {self.instance_name}, sử dụng port mặc định {self.adb_port}")

        except Exception as e:
            self.log(f"⚠️ Lỗi phát hiện ADB port: {str(e)}")

    def is_bluestacks_running(self):
        """Kiểm tra BlueStacks có đang chạy không"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                proc_name = proc.info['name'].lower()
                # Check for various BlueStacks process names
                if any(name in proc_name for name in [
                    'bluestacks', 'hd-player', 'hd-adb', 'bstkagent',
                    'hd-frontend', 'hd-agent', 'bluestacks.exe'
                ]):
                    self.log(f"🔍 Tìm thấy BlueStacks process: {proc.info['name']}")
                    return True
            return False
        except Exception as e:
            self.log(f"⚠️ Lỗi kiểm tra process: {str(e)}")
            return False

    def start_bluestacks(self):
        """Khởi động BlueStacks với instance cụ thể"""
        try:
            if not self.bluestacks_path:
                self.log("❌ Không tìm thấy đường dẫn BlueStacks")
                return False

            if self.is_bluestacks_running():
                self.log("✅ BlueStacks đã đang chạy")
                return True

            self.log(f"🚀 Đang khởi động BlueStacks với instance: {self.instance_name}")

            # Start BlueStacks with specific instance
            cmd = [self.bluestacks_path]
            if self.instance_name:
                cmd.extend(["--instance", self.instance_name])

            subprocess.Popen(cmd, shell=True)

            # Wait for BlueStacks to start
            timeout = BLUESTACKS_CONFIG['startup_timeout']
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self.is_bluestacks_running():
                    self.log("✅ BlueStacks đã khởi động thành công")
                    time.sleep(5)  # Wait a bit more for full startup
                    return True
                time.sleep(2)

            self.log("❌ Timeout khởi động BlueStacks")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi khởi động BlueStacks: {str(e)}")
            return False

    def connect_adb(self):
        """Kết nối ADB với BlueStacks"""
        try:
            if not self.adb_path:
                self.log("❌ Không tìm thấy ADB")
                return False

            # Auto-detect ADB port for the instance
            self._detect_adb_port()

            # Check if devices are already connected
            self.log(f"🔍 Kiểm tra ADB devices...")
            devices_result = subprocess.run([
                self.adb_path, "devices"
            ], capture_output=True, text=True, timeout=10)

            if "emulator-5554" in devices_result.stdout and "device" in devices_result.stdout:
                self.log("✅ ADB đã kết nối với BlueStacks")
                return True

            # Try to connect
            self.log(f"🔗 Kết nối ADB với BlueStacks instance {self.instance_name} (port {self.adb_port})...")

            result = subprocess.run([
                self.adb_path, "connect", f"127.0.0.1:{self.adb_port}"
            ], capture_output=True, text=True, timeout=10)

            if "connected" in result.stdout.lower():
                self.log("✅ Kết nối ADB thành công")
                return True
            elif "unable to connect" in result.stdout.lower() or "cannot connect" in result.stdout.lower():
                self.log(f"❌ Kết nối ADB thất bại: {result.stdout}")
                # But check devices again - sometimes it still works
                devices_result2 = subprocess.run([
                    self.adb_path, "devices"
                ], capture_output=True, text=True, timeout=5)

                if "device" in devices_result2.stdout:
                    self.log("✅ ADB thực sự đã kết nối (devices list shows connected)")
                    return True
                return False
            else:
                self.log(f"⚠️ Kết nối ADB: {result.stdout}")
                return True  # Sometimes it still works

        except Exception as e:
            self.log(f"❌ Lỗi kết nối ADB: {str(e)}")
            return False

    def launch_chrome(self):
        """Mở Chrome browser trong BlueStacks"""
        try:
            if not self.adb_path:
                return False

            self.log("🌐 Đang mở Chrome trong BlueStacks...")

            # Try method 1: Direct ADB shell command
            try:
                result = subprocess.run([
                    self.adb_path, "shell", "am", "start", "-n",
                    f"{BLUESTACKS_CONFIG['app_package']}/{BLUESTACKS_CONFIG['app_activity']}"
                ], capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    self.log("✅ Chrome đã được mở (method 1)")
                    time.sleep(3)
                    return True
                else:
                    self.log(f"⚠️ Method 1 thất bại: {result.stderr}")
            except Exception as e:
                self.log(f"⚠️ Method 1 lỗi: {str(e)}")

            # Try method 2: Tap on 13win app directly (better approach)
            self.log("🔄 Thử method 2: Tap vào app 13win.com...")

            # 13win app position based on screenshot
            app_13win_position = (411, 215)  # Exact position from screenshot

            self.log(f"👆 Click vào app 13win.com tại ({app_13win_position[0]}, {app_13win_position[1]})...")
            self._tap_screen(app_13win_position[0], app_13win_position[1])
            time.sleep(5)  # Wait for app to load

            self.log("✅ Đã click vào app 13win.com")

            # Fallback: Try Chrome if 13win app doesn't work
            self.log("🔄 Fallback: Thử Chrome nếu cần...")
            chrome_positions = [
                (257, 215),  # System apps (might have Chrome)
                (200, 300),  # Other possible positions
                (400, 300),
            ]

            for x, y in chrome_positions:
                self.log(f"👆 Fallback tap tại ({x}, {y})...")
                self._tap_screen(x, y)
                time.sleep(2)
                break

            self.log("✅ Đã thử mở Chrome (method 2)")
            self.log("💡 Vui lòng kiểm tra BlueStacks và mở Chrome thủ công nếu cần")
            return True  # Return True to continue process

        except Exception as e:
            self.log(f"❌ Lỗi mở Chrome: {str(e)}")
            self.log("💡 Vui lòng mở Chrome thủ công trong BlueStacks")
            return True  # Return True to continue process

    def navigate_to_13win(self):
        """Điều hướng đến trang 13win"""
        try:
            self.log("🌐 Truy cập 13win...")

            # Since 13win app is already installed, we might already be in the app
            # Just wait for it to load properly
            self.log("✅ App 13win.com đã được mở từ bước trước")
            self.log("⏳ Chờ app load hoàn toàn...")
            time.sleep(8)  # Wait for app to fully load

            # Check if we need to navigate to specific URL within the app
            url = BLUESTACKS_CONFIG['mobile_url']

            # Try method 1: Direct URL intent (in case app needs specific URL)
            try:
                result = subprocess.run([
                    self.adb_path, "shell", "am", "start", "-a", "android.intent.action.VIEW",
                    "-d", url
                ], capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    self.log("✅ Đã truy cập URL cụ thể trong app")
                    time.sleep(5)
                    return True
                else:
                    self.log(f"⚠️ URL intent thất bại: {result.stderr}")
            except Exception as e:
                self.log(f"⚠️ URL intent lỗi: {str(e)}")

            # Method 2: App should be ready
            self.log("✅ App 13win đã sẵn sàng")
            self.log("💡 Nếu cần, vui lòng:")
            self.log(f"   - Đảm bảo đã vào đúng trang: {url}")
            self.log("   - Tìm form đăng nhập")

            return True  # Continue process

        except Exception as e:
            self.log(f"❌ Lỗi truy cập app: {str(e)}")
            self.log("💡 Vui lòng kiểm tra app 13win trong BlueStacks")
            return True  # Continue process

    def simulate_login(self, username, password):
        """Mô phỏng đăng nhập tài khoản (sử dụng input events)"""
        try:
            if not self.adb_path:
                return False

            self.log(f"🔐 Đăng nhập tài khoản: {username}")
            self.log("💡 Hướng dẫn đăng nhập trong BlueStacks:")
            self.log(f"   👤 Tài khoản: {username}")
            self.log(f"   🔑 Mật khẩu: {password}")

            # Wait for page to load
            time.sleep(5)

            # Method 1: Try automatic input
            self.log("🔄 Thử đăng nhập tự động...")

            try:
                # Simulate tap on login area (coordinates may need adjustment)
                self.log("👆 Tìm và click vào form đăng nhập...")

                # Common login form positions
                login_positions = [
                    (400, 300),  # Top center
                    (400, 400),  # Middle center
                    (400, 500),  # Lower center
                ]

                for i, (x, y) in enumerate(login_positions):
                    self.log(f"👆 Thử tap vào vị trí đăng nhập {i+1}: ({x}, {y})")
                    self._tap_screen(x, y)
                    time.sleep(2)

                    # Try to input username
                    self._input_text(username)
                    time.sleep(1)

                    # Try next field (password)
                    self._tap_screen(x, y + 80)  # Usually password field is below username
                    time.sleep(1)
                    self._input_text(password)
                    time.sleep(1)

                    # Try login button
                    self._tap_screen(x, y + 160)  # Usually login button is below password
                    time.sleep(2)

                    break  # Try only first position for now

                self.log("✅ Đã thực hiện đăng nhập tự động")

            except Exception as e:
                self.log(f"⚠️ Đăng nhập tự động thất bại: {str(e)}")

            # Method 2: Manual guidance
            self.log("🔄 Hướng dẫn đăng nhập thủ công:")
            self.log("💡 Vui lòng thực hiện trong BlueStacks:")
            self.log("   1. Tìm form đăng nhập trên trang")
            self.log(f"   2. Nhập tài khoản: {username}")
            self.log(f"   3. Nhập mật khẩu: {password}")
            self.log("   4. Bấm nút đăng nhập")

            # Give user time to login manually
            self.log("⏳ Chờ 15 giây để bạn đăng nhập thủ công...")
            time.sleep(15)

            self.log("✅ Tiếp tục quy trình...")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi đăng nhập: {str(e)}")
            self.log("💡 Vui lòng đăng nhập thủ công trong BlueStacks")
            return True  # Continue process

    def _tap_screen(self, x, y):
        """Tap vào tọa độ trên màn hình"""
        try:
            # Try with emulator-5554 first (most common)
            result1 = subprocess.run([
                self.adb_path, "-s", "emulator-5554",
                "shell", "input", "tap", str(x), str(y)
            ], capture_output=True, timeout=5)

            if result1.returncode == 0:
                self.log(f"✅ Tap thành công tại ({x}, {y}) qua emulator-5554")
                return True

            # Fallback to port method
            result2 = subprocess.run([
                self.adb_path, "-s", f"127.0.0.1:{self.adb_port}",
                "shell", "input", "tap", str(x), str(y)
            ], capture_output=True, timeout=5)

            if result2.returncode == 0:
                self.log(f"✅ Tap thành công tại ({x}, {y}) qua port {self.adb_port}")
                return True

            # Last resort - no device specification
            result3 = subprocess.run([
                self.adb_path, "shell", "input", "tap", str(x), str(y)
            ], capture_output=True, timeout=5)

            if result3.returncode == 0:
                self.log(f"✅ Tap thành công tại ({x}, {y}) qua default device")
                return True

            self.log(f"⚠️ Tap thất bại tại ({x}, {y})")
            return False

        except Exception as e:
            self.log(f"❌ Lỗi tap: {str(e)}")
            return False

    def _input_text(self, text):
        """Nhập text"""
        try:
            # Try with emulator-5554 first
            # Clear current text first
            subprocess.run([
                self.adb_path, "-s", "emulator-5554",
                "shell", "input", "keyevent", "KEYCODE_CTRL_A"
            ], capture_output=True, timeout=5)

            # Input new text
            result = subprocess.run([
                self.adb_path, "-s", "emulator-5554",
                "shell", "input", "text", text
            ], capture_output=True, timeout=5)

            if result.returncode == 0:
                self.log(f"✅ Nhập text thành công: {text}")
                return True

            # Fallback method
            subprocess.run([
                self.adb_path, "shell", "input", "text", text
            ], capture_output=True, timeout=5)

            return True

        except Exception as e:
            self.log(f"❌ Lỗi nhập text: {str(e)}")
            return False

    def click_reward_button(self):
        """Tìm và click nút nhận thưởng 34k"""
        try:
            if not self.adb_path:
                return False

            self.log("🎁 Tìm kiếm nút nhận thưởng 34k...")

            # Wait for page to load
            time.sleep(5)

            # Method 1: Try automatic clicking
            self.log("🔄 Thử click nút thưởng tự động...")

            try:
                # Common reward button positions (may need adjustment based on screen)
                reward_coordinates = [
                    (400, 600),  # Center area
                    (400, 700),  # Lower center
                    (500, 650),  # Right center
                    (300, 650),  # Left center
                    (400, 800),  # Bottom area
                ]

                for i, (x, y) in enumerate(reward_coordinates):
                    self.log(f"👆 Thử click vị trí thưởng {i+1}: ({x}, {y})...")
                    self._tap_screen(x, y)
                    time.sleep(3)  # Wait longer to see if reward appears

                self.log("✅ Đã thực hiện click nút nhận thưởng tự động")

            except Exception as e:
                self.log(f"⚠️ Click tự động thất bại: {str(e)}")

            # Method 2: Manual guidance
            self.log("🔄 Hướng dẫn nhận thưởng thủ công:")
            self.log("💡 Vui lòng thực hiện trong BlueStacks:")
            self.log("   1. Tìm nút nhận thưởng 34k trên trang")
            self.log("   2. Bấm vào nút đó để nhận thưởng")
            self.log("   3. Xác nhận nếu có popup")

            # Give user time to claim reward manually
            self.log("⏳ Chờ 10 giây để bạn nhận thưởng thủ công...")
            time.sleep(10)

            self.log("✅ Hoàn thành quy trình nhận thưởng!")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi click nút thưởng: {str(e)}")
            self.log("💡 Vui lòng nhận thưởng thủ công trong BlueStacks")
            return True  # Continue process

    def auto_process_account(self, username, password):
        """
        Quy trình tự động hoàn chỉnh:
        1. Khởi động BlueStacks
        2. Mở Chrome
        3. Truy cập 13win
        4. Đăng nhập
        5. Nhận thưởng
        """
        try:
            self.log("🤖 Bắt đầu quy trình tự động BlueStacks...")

            # Step 1: Start BlueStacks
            if not self.start_bluestacks():
                return False

            # Step 2: Connect ADB
            if not self.connect_adb():
                return False

            # Step 3: Launch Chrome
            if not self.launch_chrome():
                return False

            # Step 4: Navigate to 13win
            if not self.navigate_to_13win():
                return False

            # Step 5: Login
            if not self.simulate_login(username, password):
                self.log("⚠️ Đăng nhập có thể cần thực hiện thủ công")

            # Step 6: Click reward button
            if BLUESTACKS_CONFIG['auto_click_reward']:
                time.sleep(5)  # Wait for login to complete
                if not self.click_reward_button():
                    self.log("⚠️ Nhận thưởng có thể cần thực hiện thủ công")

            self.log("🎉 Hoàn thành quy trình BlueStacks!")
            self.log("💡 Vui lòng kiểm tra BlueStacks để xác nhận kết quả")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi quy trình tự động: {str(e)}")
            return False

    def get_screen_size(self):
        """Lấy kích thước màn hình BlueStacks"""
        try:
            if not self.adb_path:
                return None

            # Try with emulator-5554 first
            result = subprocess.run([
                self.adb_path, "-s", "emulator-5554",
                "shell", "wm", "size"
            ], capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and result.stdout:
                # Parse output like "Physical size: 1920x1080"
                match = re.search(r'(\d+)x(\d+)', result.stdout)
                if match:
                    width, height = int(match.group(1)), int(match.group(2))
                    self.log(f"📱 Kích thước màn hình: {width}x{height}")
                    return (width, height)

            # Fallback method
            result2 = subprocess.run([
                self.adb_path, "shell", "wm", "size"
            ], capture_output=True, text=True, timeout=5)

            if result2.returncode == 0 and result2.stdout:
                match = re.search(r'(\d+)x(\d+)', result2.stdout)
                if match:
                    width, height = int(match.group(1)), int(match.group(2))
                    self.log(f"📱 Kích thước màn hình: {width}x{height}")
                    return (width, height)

            # Default size if can't detect
            self.log("⚠️ Không lấy được kích thước màn hình, sử dụng mặc định 1920x1080")
            return (1920, 1080)

        except Exception as e:
            self.log(f"❌ Lỗi lấy kích thước màn hình: {str(e)}")
            return (1920, 1080)  # Default size

    def run_automation(self, username, password):
        """Chạy quy trình automation hoàn chỉnh"""
        return self.auto_process_account(username, password)


def test_bluestacks_automation():
    """Test function for BlueStacks automation"""
    print("🧪 Testing BlueStacks Automation...")

    automation = BlueStacksAutomation()

    # Test detection
    print(f"BlueStacks path: {automation.bluestacks_path}")
    print(f"ADB path: {automation.adb_path}")

    # Test if BlueStacks is running
    if automation.is_bluestacks_running():
        print("✅ BlueStacks is running")

        # Test ADB connection
        print("🔗 Testing ADB connection...")
        try:
            result = subprocess.run([
                automation.adb_path, "devices"
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print("✅ ADB connection successful")
                print(f"Devices: {result.stdout}")
            else:
                print(f"❌ ADB connection failed: {result.stderr}")
        except Exception as e:
            print(f"❌ ADB test error: {str(e)}")

        # Test screen size
        print("📱 Testing screen size...")
        screen_size = automation.get_screen_size()
        if screen_size:
            print(f"✅ Screen size: {screen_size}")
        else:
            print("❌ Could not get screen size")

        # Test tap functionality
        print("👆 Testing tap functionality...")
        print("Will tap at center of screen in 3 seconds...")
        time.sleep(3)

        if screen_size:
            center_x = screen_size[0] // 2
            center_y = screen_size[1] // 2
            automation._tap_screen(center_x, center_y)
            print(f"✅ Tapped at ({center_x}, {center_y})")

    else:
        print("❌ BlueStacks is not running")
        print("💡 Please start BlueStacks first")


def run_full_automation():
    """Run the full automation process"""
    print("🚀 Starting Full BlueStacks Automation...")

    automation = BlueStacksAutomation()

    # Test credentials
    test_username = "testuser"
    test_password = "testpass"

    success = automation.run_automation(test_username, test_password)

    if success:
        print("✅ Automation completed successfully!")
    else:
        print("❌ Automation failed!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "full":
        run_full_automation()
    else:
        test_bluestacks_automation()
