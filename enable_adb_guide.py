#!/usr/bin/env python3
"""
Hướng dẫn bật ADB Debugging trong BlueStacks
"""

import subprocess
import time

def check_bluestacks_status():
    """Kiểm tra trạng thái BlueStacks"""
    print("🔍 Kiểm tra trạng thái BlueStacks...")
    
    # Kiểm tra BlueStacks có chạy không
    try:
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq HD-Player.exe'],
            capture_output=True, text=True
        )
        
        if 'HD-Player.exe' in result.stdout:
            print("✅ BlueStacks đang chạy")
            return True
        else:
            print("❌ BlueStacks chưa chạy")
            return False
    except:
        print("❌ Không thể kiểm tra BlueStacks")
        return False

def check_adb_devices():
    """Kiểm tra ADB devices"""
    print("\n🔍 Kiểm tra ADB devices...")
    
    try:
        result = subprocess.run([
            "C:\\Program Files\\BlueStacks_nxt\\HD-Adb.exe", "devices"
        ], capture_output=True, text=True, timeout=10)
        
        print("ADB Output:")
        print(result.stdout)
        
        if 'device' in result.stdout and 'emulator' in result.stdout:
            print("✅ ADB đã phát hiện BlueStacks")
            return True
        else:
            print("❌ ADB chưa phát hiện BlueStacks")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi kiểm tra ADB: {str(e)}")
        return False

def show_adb_guide():
    """Hiển thị hướng dẫn bật ADB debugging"""
    print("\n" + "="*60)
    print("📱 HƯỚNG DẪN BẬT ADB DEBUGGING TRONG BLUESTACKS")
    print("="*60)
    
    print("\n🔧 Bước 1: Mở BlueStacks")
    print("   - Đảm bảo BlueStacks đang chạy")
    print("   - Nếu chưa chạy, khởi động BlueStacks")
    
    print("\n🔧 Bước 2: Vào Settings")
    print("   - Click vào biểu tượng Settings (bánh răng) trong BlueStacks")
    print("   - Hoặc kéo xuống từ trên cùng màn hình → Settings")
    
    print("\n🔧 Bước 3: Tìm 'About Phone' hoặc 'About'")
    print("   - Scroll xuống tìm 'About phone' hoặc 'About'")
    print("   - Click vào mục này")
    
    print("\n🔧 Bước 4: Bật Developer Options")
    print("   - Tìm 'Build number' hoặc 'Build version'")
    print("   - TAP 7 LẦN liên tiếp vào 'Build number'")
    print("   - Sẽ có thông báo 'You are now a developer!'")
    
    print("\n🔧 Bước 5: Bật USB Debugging")
    print("   - Quay lại Settings chính")
    print("   - Tìm 'Developer options' (mới xuất hiện)")
    print("   - Click vào 'Developer options'")
    print("   - Tìm 'USB debugging' hoặc 'ADB debugging'")
    print("   - BẬT tùy chọn này")
    
    print("\n🔧 Bước 6: Khởi động lại BlueStacks")
    print("   - Đóng BlueStacks hoàn toàn")
    print("   - Khởi động lại BlueStacks")
    print("   - Chờ BlueStacks khởi động hoàn toàn")
    
    print("\n" + "="*60)
    print("💡 LƯU Ý QUAN TRỌNG:")
    print("   - Phải TAP chính xác 7 lần vào Build number")
    print("   - Phải khởi động lại BlueStacks sau khi bật")
    print("   - Nếu không thấy Developer options, thử lại từ đầu")
    print("="*60)

def test_after_enable():
    """Test sau khi bật ADB debugging"""
    print("\n🧪 Test sau khi bật ADB debugging...")
    
    input("\n⏳ Nhấn Enter sau khi đã bật ADB debugging và khởi động lại BlueStacks...")
    
    # Kiểm tra lại
    if check_bluestacks_status():
        time.sleep(3)
        if check_adb_devices():
            print("\n🎉 THÀNH CÔNG! ADB debugging đã hoạt động!")
            print("✅ Bây giờ có thể sử dụng tính năng BlueStacks automation")
            return True
        else:
            print("\n❌ ADB vẫn chưa hoạt động")
            print("💡 Hãy kiểm tra lại các bước trên")
            return False
    else:
        print("\n❌ BlueStacks chưa chạy")
        return False

def main():
    """Main function"""
    print("🚀 BlueStacks ADB Debugging Setup Guide")
    print("=" * 50)
    
    # Kiểm tra trạng thái hiện tại
    bluestacks_running = check_bluestacks_status()
    adb_working = False
    
    if bluestacks_running:
        adb_working = check_adb_devices()
    
    if adb_working:
        print("\n🎉 ADB debugging đã hoạt động!")
        print("✅ BlueStacks automation sẵn sàng sử dụng!")
    else:
        print("\n⚠️ ADB debugging chưa hoạt động")
        show_adb_guide()
        test_after_enable()

if __name__ == "__main__":
    main()
