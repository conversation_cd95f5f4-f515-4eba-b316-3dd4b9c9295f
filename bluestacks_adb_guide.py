#!/usr/bin/env python3
"""
Hướng dẫn chi tiết bật ADB debugging trong BlueStacks
"""

def show_detailed_guide():
    print("🔧 HƯỚNG DẪN BẬT ADB DEBUGGING TRONG BLUESTACKS")
    print("=" * 60)
    
    print("\n📱 PHƯƠNG PHÁP 1: Qua Android Settings")
    print("-" * 40)
    print("1. Mở BlueStacks")
    print("2. Tìm app 'Settings' (biểu tượng bánh răng)")
    print("3. Mở Settings app")
    print("4. Scroll xuống tìm 'System' hoặc 'About phone'")
    print("5. Click vào 'About phone' hoặc 'About tablet'")
    print("6. Tìm 'Build number' hoặc 'Build version'")
    print("   - C<PERSON> thể ở cuối danh sách")
    print("   - C<PERSON> thể trong mục 'Software information'")
    print("7. TAP 7 LẦN liên tiếp vào 'Build number'")
    print("8. Sẽ có thông báo 'You are now a developer!'")
    print("9. Quay lại Settings chính")
    print("10. Tìm 'Developer options' (mới xuất hiện)")
    print("11. Bật 'USB debugging' hoặc 'Android debugging'")
    
    print("\n📱 PHƯƠNG PHÁP 2: Qua BlueStacks Settings")
    print("-" * 40)
    print("1. Click vào biểu tượng BlueStacks ở góc phải dưới")
    print("2. Chọn 'Settings' hoặc 'Preferences'")
    print("3. Tìm tab 'Advanced' hoặc 'Engine'")
    print("4. Tìm tùy chọn 'Enable Android Debug Bridge (ADB)'")
    print("5. Bật tùy chọn này")
    print("6. Khởi động lại BlueStacks")
    
    print("\n📱 PHƯƠNG PHÁP 3: Qua Multi-Instance Manager")
    print("-" * 40)
    print("1. Mở BlueStacks Multi-Instance Manager")
    print("2. Click vào biểu tượng Settings của instance Pie64")
    print("3. Tìm tab 'Advanced Settings'")
    print("4. Bật 'Enable ADB debugging'")
    print("5. Save và restart instance")
    
    print("\n📱 PHƯƠNG PHÁP 4: Nếu không tìm thấy Build number")
    print("-" * 40)
    print("1. Trong Settings, tìm 'About'")
    print("2. Thử các mục sau:")
    print("   - About phone")
    print("   - About tablet") 
    print("   - About device")
    print("   - System → About phone")
    print("   - General → About")
    print("3. Trong mục About, tìm:")
    print("   - Build number")
    print("   - Build version")
    print("   - Software version")
    print("   - Android version (tap 7 lần)")
    print("4. Nếu vẫn không có, thử tap 7 lần vào:")
    print("   - Android version")
    print("   - Kernel version")
    print("   - Model number")
    
    print("\n🔍 CÁCH KIỂM TRA ĐÃ THÀNH CÔNG")
    print("-" * 40)
    print("1. Quay lại Settings chính")
    print("2. Scroll xuống, sẽ thấy 'Developer options'")
    print("3. Vào Developer options")
    print("4. Tìm và bật 'USB debugging'")
    print("5. Có thể có popup xác nhận → chọn OK")
    
    print("\n⚠️ LƯU Ý QUAN TRỌNG")
    print("-" * 40)
    print("- Phải tap chính xác 7 lần")
    print("- Tap nhanh liên tiếp")
    print("- Nếu không thành công, thử lại từ đầu")
    print("- Một số phiên bản BlueStacks có thể khác")
    print("- Khởi động lại BlueStacks sau khi bật")
    
    print("\n🧪 TEST SAU KHI BẬT")
    print("-" * 40)
    print("Chạy lệnh: python test_simple_automation.py")
    print("Nếu thành công sẽ thấy: '✅ ADB kết nối thành công!'")

def check_bluestacks_version():
    """Kiểm tra phiên bản BlueStacks"""
    import subprocess
    import os
    
    print("\n🔍 KIỂM TRA PHIÊN BẢN BLUESTACKS")
    print("-" * 40)
    
    # Kiểm tra file version
    bluestacks_paths = [
        r"C:\Program Files\BlueStacks_nxt\HD-Player.exe",
        r"C:\Program Files (x86)\BlueStacks_nxt\HD-Player.exe",
        r"C:\Program Files\BlueStacks\HD-Player.exe"
    ]
    
    for path in bluestacks_paths:
        if os.path.exists(path):
            print(f"✅ Tìm thấy BlueStacks: {path}")
            
            # Thử lấy version info
            try:
                result = subprocess.run([
                    'powershell', 
                    f'(Get-ItemProperty "{path}").VersionInfo.FileVersion'
                ], capture_output=True, text=True)
                
                if result.stdout.strip():
                    print(f"📋 Version: {result.stdout.strip()}")
                    
                    # Gợi ý dựa trên version
                    version = result.stdout.strip()
                    if "5." in version:
                        print("💡 BlueStacks 5: Thử phương pháp 1 hoặc 2")
                    elif "4." in version:
                        print("💡 BlueStacks 4: Thử phương pháp 2 hoặc 3")
                    else:
                        print("💡 Phiên bản khác: Thử tất cả phương pháp")
                        
            except:
                print("⚠️ Không lấy được thông tin version")
            break
    else:
        print("❌ Không tìm thấy BlueStacks")

def main():
    print("🚀 BlueStacks ADB Debugging - Hướng dẫn chi tiết")
    print("=" * 60)
    
    check_bluestacks_version()
    show_detailed_guide()
    
    print("\n" + "=" * 60)
    print("💬 Nếu vẫn không được, hãy cho biết:")
    print("   - Phiên bản BlueStacks")
    print("   - Những gì bạn thấy trong Settings")
    print("   - Screenshot nếu có thể")
    print("=" * 60)

if __name__ == "__main__":
    main()
