#!/usr/bin/env python3
"""
Test đơn giản cho BlueStacks automation
"""

from bluestacks_automation import BlueStacksAutomation
import time

def test_simple():
    """Test automation đơn giản"""
    print("🧪 Test BlueStacks Automation - Đơn giản")
    print("=" * 50)
    
    # Tạo automation instance
    automation = BlueStacksAutomation()
    
    print("📋 Thông tin cấu hình:")
    print(f"   📱 Instance: {automation.instance_name}")
    print(f"   🔌 Port: {automation.adb_port}")
    print(f"   📂 BlueStacks: {automation.bluestacks_path}")
    print(f"   🔧 ADB: {automation.adb_path}")
    print()
    
    # Test 1: Kiểm tra BlueStacks có chạy không
    print("🔍 Test 1: Kiểm tra BlueStacks...")
    if automation.is_bluestacks_running():
        print("✅ BlueStacks đang chạy")
        
        # Test 2: Thử kết nối ADB
        print("\n🔍 Test 2: Thử kết nối ADB...")
        if automation.connect_adb():
            print("✅ ADB kết nối thành công!")
            
            # Test 3: L<PERSON>y kích thước màn hình
            print("\n🔍 Test 3: Lấy thông tin màn hình...")
            screen_size = automation.get_screen_size()
            if screen_size:
                print(f"✅ Kích thước màn hình: {screen_size[0]}x{screen_size[1]}")
            else:
                print("⚠️ Không lấy được kích thước màn hình")
            
            print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
            print("✅ BlueStacks automation sẵn sàng sử dụng!")
            
        else:
            print("❌ ADB kết nối thất bại")
            print("💡 Cần bật ADB debugging trong BlueStacks")
            
    else:
        print("❌ BlueStacks chưa chạy")
        
        # Test khởi động BlueStacks
        print("\n🔍 Test: Thử khởi động BlueStacks...")
        if automation.start_bluestacks():
            print("✅ Khởi động BlueStacks thành công!")
            print("⏳ Chờ 15 giây để BlueStacks khởi động...")
            time.sleep(15)
            
            # Thử lại kết nối ADB
            print("\n🔍 Thử kết nối ADB sau khi khởi động...")
            if automation.connect_adb():
                print("✅ ADB kết nối thành công!")
                print("🎉 BlueStacks automation hoạt động!")
            else:
                print("❌ ADB vẫn chưa kết nối được")
                print("💡 Cần bật ADB debugging trong BlueStacks")
        else:
            print("❌ Không thể khởi động BlueStacks")
    
    print("\n" + "="*50)
    print("📋 KẾT LUẬN:")
    print("   - Nếu tất cả test thành công → Có thể sử dụng automation")
    print("   - Nếu ADB thất bại → Cần bật ADB debugging")
    print("   - Nếu khởi động thất bại → Kiểm tra cài đặt BlueStacks")
    print("="*50)

if __name__ == "__main__":
    test_simple()
