#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🖱️ MOUSE AUTOMATION - Automation dùng mouse clicks trực tiếp

Thay vì dùng ADB, script này sẽ:
1. <PERSON><PERSON><PERSON> cửa sổ BlueStacks
2. <PERSON><PERSON> tr<PERSON><PERSON> tiếp bằng mouse
3. <PERSON><PERSON><PERSON><PERSON> text bằng keyboard
4. <PERSON><PERSON>t động như người dùng thật
"""

import time
import pyautogui
import pygetwindow as gw
import sys
import subprocess

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

class MouseAutomation:
    def __init__(self):
        self.bluestacks_window = None
        self.window_offset = (0, 0)
        
    def log(self, message):
        """In log với timestamp"""
        current_time = time.strftime("%H:%M:%S")
        print(f"[{current_time}] {message}")
    
    def find_bluestacks_window(self):
        """T<PERSON><PERSON> cửa sổ BlueStacks"""
        try:
            self.log("🔍 <PERSON><PERSON><PERSON> cửa sổ BlueStacks...")
            
            # T<PERSON><PERSON> c<PERSON><PERSON> cử<PERSON> sổ có thể là BlueStacks
            windows = gw.getWindowsWithTitle("BlueStacks")
            if not windows:
                windows = gw.getWindowsWithTitle("BlueStacks App Player")
            if not windows:
                windows = gw.getWindowsWithTitle("Pie64")
            
            if windows:
                self.bluestacks_window = windows[0]
                self.log(f"✅ Tìm thấy BlueStacks: {self.bluestacks_window.title}")
                self.log(f"   Vị trí: ({self.bluestacks_window.left}, {self.bluestacks_window.top})")
                self.log(f"   Kích thước: {self.bluestacks_window.width}x{self.bluestacks_window.height}")
                
                # Calculate offset for clicks
                self.window_offset = (self.bluestacks_window.left, self.bluestacks_window.top)
                return True
            else:
                self.log("❌ Không tìm thấy cửa sổ BlueStacks!")
                self.log("💡 Đảm bảo BlueStacks đang mở và hiển thị")
                return False
                
        except Exception as e:
            self.log(f"❌ Lỗi tìm cửa sổ: {str(e)}")
            return False
    
    def focus_bluestacks(self):
        """Focus vào cửa sổ BlueStacks"""
        try:
            if self.bluestacks_window:
                self.bluestacks_window.activate()
                time.sleep(1)
                self.log("✅ Đã focus vào BlueStacks")
                return True
            return False
        except Exception as e:
            self.log(f"❌ Lỗi focus: {str(e)}")
            return False
    
    def click_at(self, x, y, description=""):
        """Click tại tọa độ (relative to BlueStacks window)"""
        try:
            if not self.bluestacks_window:
                return False
            
            # Calculate absolute coordinates
            abs_x = self.window_offset[0] + x
            abs_y = self.window_offset[1] + y
            
            self.log(f"👆 Click {description} tại ({x}, {y}) -> ({abs_x}, {abs_y})")
            
            # Focus window first
            self.focus_bluestacks()
            
            # Click
            pyautogui.click(abs_x, abs_y)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi click: {str(e)}")
            return False
    
    def type_text(self, text, description=""):
        """Nhập text"""
        try:
            self.log(f"📝 Nhập {description}: {text}")
            
            # Clear existing text first
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            
            # Type new text
            pyautogui.write(text, interval=0.05)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi nhập text: {str(e)}")
            return False
    
    def run_13win_automation(self, username, password):
        """Chạy automation cho 13win"""
        try:
            self.log("🚀 BẮT ĐẦU MOUSE AUTOMATION CHO 13WIN")
            self.log("=" * 50)
            
            # Step 1: Find BlueStacks window
            if not self.find_bluestacks_window():
                return False
            
            # Step 2: Focus BlueStacks
            if not self.focus_bluestacks():
                return False
            
            # Step 3: Click on 13win app
            self.log("\n📱 BƯỚC 1: MỞ APP 13WIN")
            self.log("-" * 30)
            
            # Click on 13win app icon (coordinates from screenshot)
            if self.click_at(411, 215, "app 13win.com"):
                self.log("✅ Đã click vào app 13win")
                time.sleep(5)  # Wait for app to load
            else:
                self.log("❌ Không thể click vào app 13win")
                return False
            
            # Step 4: Wait for app to load
            self.log("\n⏳ BƯỚC 2: CHỜ APP LOAD")
            self.log("-" * 30)
            self.log("Chờ 8 giây để app load hoàn toàn...")
            time.sleep(8)
            
            # Step 5: Login process
            self.log("\n🔐 BƯỚC 3: ĐĂNG NHẬP")
            self.log("-" * 30)
            
            # Try different login positions
            login_positions = [
                (400, 300, "form đăng nhập 1"),
                (400, 400, "form đăng nhập 2"),
                (400, 500, "form đăng nhập 3"),
            ]
            
            for x, y, desc in login_positions:
                self.log(f"\n🔄 Thử {desc}...")
                
                # Click username field
                if self.click_at(x, y, f"username field - {desc}"):
                    time.sleep(1)
                    
                    # Type username
                    if self.type_text(username, "username"):
                        time.sleep(1)
                        
                        # Click password field (usually below username)
                        if self.click_at(x, y + 80, f"password field - {desc}"):
                            time.sleep(1)
                            
                            # Type password
                            if self.type_text(password, "password"):
                                time.sleep(1)
                                
                                # Click login button (usually below password)
                                if self.click_at(x, y + 160, f"login button - {desc}"):
                                    self.log("✅ Đã thực hiện đăng nhập")
                                    time.sleep(3)
                                    break
                
                self.log(f"⚠️ {desc} không thành công, thử vị trí khác...")
            
            # Step 6: Wait for login to complete
            self.log("\n⏳ BƯỚC 4: CHỜ ĐĂNG NHẬP HOÀN THÀNH")
            self.log("-" * 30)
            self.log("Chờ 10 giây để đăng nhập hoàn thành...")
            time.sleep(10)
            
            # Step 7: Look for reward button
            self.log("\n🎁 BƯỚC 5: TÌM VÀ NHẬN THƯỞNG 34K")
            self.log("-" * 30)
            
            # Try different reward button positions
            reward_positions = [
                (400, 600, "nút thưởng center"),
                (400, 700, "nút thưởng lower center"),
                (500, 650, "nút thưởng right"),
                (300, 650, "nút thưởng left"),
                (400, 800, "nút thưởng bottom"),
            ]
            
            for x, y, desc in reward_positions:
                self.log(f"👆 Thử click {desc}...")
                if self.click_at(x, y, desc):
                    time.sleep(3)  # Wait to see if reward appears
            
            # Step 8: Complete
            self.log("\n🎉 BƯỚC 6: HOÀN THÀNH")
            self.log("-" * 30)
            self.log("✅ Đã hoàn thành tất cả các bước automation!")
            self.log("💡 Vui lòng kiểm tra BlueStacks để xác nhận:")
            self.log("   - Đã đăng nhập thành công chưa?")
            self.log("   - Đã nhận được thưởng 34k chưa?")
            
            return True
            
        except Exception as e:
            self.log(f"❌ Lỗi automation: {str(e)}")
            return False

def main():
    """Hàm chính"""
    try:
        print("🖱️ MOUSE AUTOMATION CHO 13WIN")
        print("=" * 50)
        print("💡 Automation này sử dụng mouse clicks trực tiếp")
        print("💡 Không cần ADB, hoạt động như người dùng thật")
        print("⚠️ Vui lòng không di chuyển chuột trong quá trình chạy")
        print()
        
        # Get credentials
        username = input("👤 Nhập tài khoản 13win: ").strip()
        if not username:
            print("❌ Tài khoản không được để trống!")
            return
        
        password = input("🔑 Nhập mật khẩu: ").strip()
        if not password:
            print("❌ Mật khẩu không được để trống!")
            return
        
        print(f"\n✅ Sẽ đăng nhập với tài khoản: {username}")
        
        # Confirm
        confirm = input("\n❓ Bắt đầu automation? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'có']:
            print("👋 Hủy automation!")
            return
        
        # Run automation
        automation = MouseAutomation()
        success = automation.run_13win_automation(username, password)
        
        if success:
            print("\n🎉 AUTOMATION HOÀN THÀNH!")
            print("💰 Hãy kiểm tra BlueStacks để xác nhận kết quả")
        else:
            print("\n❌ AUTOMATION THẤT BẠI!")
            print("💡 Vui lòng thực hiện thủ công hoặc thử lại")
        
    except KeyboardInterrupt:
        print("\n\n👋 Automation bị dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi: {str(e)}")
        print("💡 Đảm bảo đã cài đặt: pip install pyautogui pygetwindow")

if __name__ == "__main__":
    main()
