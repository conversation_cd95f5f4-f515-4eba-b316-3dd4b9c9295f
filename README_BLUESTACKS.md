# 🎉 TÍNH NĂNG MỚI: BLUESTACKS AUTOMATION

## 📱 Tổng quan
Tool run_simple đã được cập nhật với tính năng **tự động BlueStacks**! Sau khi đăng ký tài khoản thành công trên Chrome (PC), tool sẽ tự động:

1. ✅ Khởi động BlueStacks với instance Pie64
2. ✅ Mở Chrome trong BlueStacks  
3. ✅ Truy cập trang 13win
4. ✅ Đăng nhập tài khoản vừa tạo
5. ✅ Tự động bấm nút nhận 34k

## 🚀 Cách sử dụng nhanh

### Bước 1: Cài đặt BlueStacks
```bash
# Tải từ: https://www.bluestacks.com/
# Cài đặt với instance Pie64 (khuyến nghị)
```

### Bước 2: Cài thư viện
```bash
pip install psutil
```

### Bước 3: Bật ADB Debugging
```
BlueStacks → Settings → About → Tap 7 lần "Build Number"
→ Settings → Developer Options → Bật "USB Debugging"
```

### Bước 4: Sử dụng tool
```bash
python run_simple.py
```
- ✅ Tích chọn "📱 Tự động mở BlueStacks và đăng nhập"
- ✅ Bấm "🚀 Bắt đầu đăng ký tự động"

## 🔧 Cấu hình

### Instance BlueStacks
Tool được cấu hình cho **instance Pie64** (Android 9, 64-bit):
```python
# config.py
'instance_name': 'Pie64',  # Instance name
'adb_port': 5556,          # Port cho Pie64
```

### Các instance khác
Nếu bạn sử dụng instance khác, cập nhật trong `config.py`:
```python
# Pie64 (Android 9) - Port 5556 - Khuyến nghị
# Nougat64 (Android 7) - Port 5555
# Nougat32 (Android 7) - Port 5557
# Android11 (Android 11) - Port 5559
```

## 🧪 Test tính năng

### Test cơ bản
```bash
python bluestacks_automation.py
```

### Test với Pie64
```bash
python test_bluestacks_pie64.py
```

### Test tool chính
```bash
python run_simple.py
# Tích chọn BlueStacks option và test
```

## 📁 Files mới

- `bluestacks_automation.py` - Module BlueStacks automation
- `test_bluestacks_pie64.py` - Script test
- `BLUESTACKS_GUIDE.md` - Hướng dẫn chi tiết
- `README_BLUESTACKS.md` - File này

## 🔍 Xử lý sự cố

### ❌ "BlueStacks automation không khả dụng"
```bash
pip install psutil
```

### ❌ "Không tìm thấy BlueStacks"
- Cài đặt BlueStacks từ trang chính thức
- Hoặc cấu hình đường dẫn trong config.py

### ❌ "Không thể kết nối ADB"
- Bật ADB debugging trong BlueStacks
- Khởi động lại BlueStacks
- Kiểm tra port ADB

### ❌ "Instance không đúng"
- Mở BlueStacks Multi-Instance Manager
- Kiểm tra tên instance
- Cập nhật trong config.py

## 💡 Lưu ý quan trọng

### ✅ Ưu điểm
- Tự động hoàn toàn từ đăng ký đến nhận thưởng
- Hỗ trợ nhiều instance BlueStacks
- Có thể tắt tính năng nếu không cần

### ⚠️ Hạn chế
- Cần cài đặt và cấu hình BlueStacks
- Tọa độ click có thể cần điều chỉnh
- Phụ thuộc vào tốc độ internet

### 🎯 Khuyến nghị
- Sử dụng instance Pie64 (ổn định nhất)
- Để BlueStacks mở sẵn để tăng tốc độ
- Kiểm tra log để theo dõi tiến trình

## 🔄 Quy trình hoàn chỉnh

```
1. Khởi động run_simple.py
   ↓
2. Tích chọn "📱 Tự động mở BlueStacks"
   ↓
3. Tool đăng ký tài khoản trên Chrome (PC)
   ↓
4. Đăng ký thành công → Khởi động BlueStacks
   ↓
5. Mở Chrome trong BlueStacks
   ↓
6. Truy cập 13win và đăng nhập
   ↓
7. Tự động nhận thưởng 34k
   ↓
8. Hoàn thành! 🎉
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Đọc `BLUESTACKS_GUIDE.md` để biết chi tiết
2. Chạy test scripts để kiểm tra
3. Kiểm tra log trong tool
4. Đảm bảo BlueStacks và ADB hoạt động bình thường

---
**Chúc bạn sử dụng tool hiệu quả! 🚀**
