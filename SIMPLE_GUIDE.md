# 🚀 HƯỚNG DẪN SIMPLE AUTO REGISTER TOOL

## 🎯 M<PERSON><PERSON> đích
Tool đơn giản chỉ tập trung vào việc tự động đăng ký tài khoản 13win với 3 bước đơn giản.

## 🚀 Khởi chạy nhanh

### Windows:
```bash
# Cách 1: Nhấp đúp
start_simple.bat

# Cách 2: Command line
python run_simple.py
```

### Linux/Mac:
```bash
python run_simple.py
```

## 📋 Giao diện đơn giản

### 🌐 Website
- **URL**: Địa chỉ website (mặc định: https://www.13win16.com/?id=*********)
- <PERSON><PERSON> thể thay đổi nếu cần

### 📝 Thông tin đăng ký
- **Tài khoản**: Để trống = tự động tạo (user1234)
- **Mật khẩu**: Để trống = tự động tạo (8 ký tự ngẫu nhiên)
- **Họ và tên**: Mặc định "TRAN HOANG AN" (c<PERSON> thể sửa)
- **Tên ngân hàng**: Chọn từ dropdown (VIETCOMBANK, TECHCOMBANK, etc.)

### 📱 Tính năng BlueStacks (MỚI!)
- **🎁 Tự động nhận thưởng**: Tự động nhận thưởng sau khi đăng ký
- **📱 Tự động mở BlueStacks**: Tự động mở BlueStacks và đăng nhập tài khoản vừa tạo
  - Khởi động BlueStacks
  - Mở Chrome trong BlueStacks
  - Truy cập trang 13win
  - Đăng nhập tài khoản
  - Tự động bấm nút nhận 34k

### 🎮 3 Bước đơn giản
1. **🚀 Bước 1: Khởi động Chrome** - Mở trình duyệt
2. **🌐 Bước 2: Truy cập 13win** - Vào website
3. **🤖 Bước 3: Tự động Đăng ký** - Điền form và submit

## 📖 Hướng dẫn sử dụng chi tiết

### Bước 1: Chuẩn bị
1. Khởi chạy tool: `python run_simple.py`
2. Kiểm tra thông tin đăng ký (có thể để mặc định)
3. Nhấn "🚀 Bước 1: Khởi động Chrome"

### Bước 2: Truy cập website
1. Chờ Chrome khởi động xong
2. Nhấn "🌐 Bước 2: Truy cập 13win"
3. Tool sẽ tự động thử nhiều URL:
   - https://www.13win16.com/?id=*********
   - https://www.13win20.com/?id=*********
   - https://13win.com/?id=*********
   - https://13win.net/?id=*********

### Bước 3: Tự động đăng ký
1. Chờ website load xong
2. Nhấn "🤖 Bước 3: Tự động Đăng ký"
3. Tool sẽ tự động:
   - Tạo tài khoản và mật khẩu (nếu để trống)
   - Tìm và điền form đăng ký
   - Điền tài khoản, mật khẩu, nhập lại mật khẩu
   - Điền họ tên và tên ngân hàng
   - Click nút đăng ký

### Bước 4: Kiểm tra kết quả
1. Xem log để biết tiến độ
2. Kiểm tra trang web có đăng ký thành công không
3. Lưu lại thông tin tài khoản đã tạo

## 🔧 Tính năng thông minh

### 🔍 Tự động tìm URL hoạt động
- Thử nhiều domain khác nhau
- Tự động chuyển sang URL backup nếu chính không hoạt động
- Kiểm tra trang có load đúng không

### 🤖 Tự động điền form
- **Tìm trường tài khoản**: `input[name*="username"]`, `input[placeholder*="tài khoản"]`
- **Tìm trường mật khẩu**: `input[type="password"]`, `input[name*="password"]`
- **Tìm trường họ tên**: `input[name*="name"]`, `input[placeholder*="họ tên"]`
- **Tìm trường ngân hàng**: `input[name*="bank"]`, `select[name*="bank"]`
- **Tìm nút submit**: `button[type="submit"]`, button có text "Đăng ký"

### 🎲 Tự động tạo thông tin
- **Username**: user + 4 số ngẫu nhiên (vd: user1234)
- **Password**: 8 ký tự chữ và số ngẫu nhiên (vd: aB3dE7gH)
- **Tự động điền lại**: Hiển thị thông tin đã tạo trong form

## 📊 Log chi tiết

### ✅ Thành công:
```
[14:30:15] 🚀 Đang khởi động Chrome...
[14:30:18] ✅ Chrome đã khởi động thành công!
[14:30:20] 🌐 Đang truy cập website...
[14:30:22] 🔗 Thử truy cập: https://www.13win16.com/?id=*********
[14:30:25] ✅ Truy cập thành công: https://www.13win16.com/register
[14:30:25] 📄 Tiêu đề: 13win - Đăng ký
[14:30:27] 🤖 Bắt đầu tự động đăng ký...
[14:30:27] 📝 Thông tin đăng ký:
[14:30:27]    👤 Tài khoản: user1234
[14:30:27]    🔑 Mật khẩu: aB3dE7gH
[14:30:27]    👨 Họ tên: TRAN HOANG AN
[14:30:27]    🏦 Ngân hàng: VIETCOMBANK
[14:30:28] ✅ Đã điền tài khoản: user1234
[14:30:29] ✅ Đã điền mật khẩu
[14:30:30] ✅ Đã điền nhập lại mật khẩu
[14:30:31] ✅ Đã điền họ tên: TRAN HOANG AN
[14:30:32] ✅ Đã điền tên ngân hàng: VIETCOMBANK
[14:30:34] ✅ Đã click nút đăng ký!
[14:30:39] 🎉 Quá trình đăng ký hoàn thành!
```

### ⚠️ Cảnh báo:
```
[14:30:25] ⚠️ Không tìm thấy trường tên ngân hàng
[14:30:34] ⚠️ Không tìm thấy nút đăng ký, vui lòng click thủ công
```

## 🔄 Đăng ký nhiều tài khoản

### Cách 1: Thủ công
1. Hoàn thành đăng ký tài khoản đầu tiên
2. Chọn "Yes" khi được hỏi có muốn đăng ký tiếp
3. Tool sẽ reset form và sẵn sàng cho tài khoản mới
4. Nhấn "🤖 Bước 3: Tự động Đăng ký" lại

### Cách 2: Đóng và mở lại Chrome
1. Đóng Chrome sau khi đăng ký xong
2. Nhấn "🚀 Bước 1: Khởi động Chrome" lại
3. Lặp lại quy trình

## 💡 Tips sử dụng

### 🎯 Tăng tỷ lệ thành công:
- Kiểm tra website có hoạt động thủ công trước
- Để trống tài khoản/mật khẩu để tool tự tạo
- Chọn ngân hàng phổ biến (VIETCOMBANK, TECHCOMBANK)
- Chờ đủ thời gian giữa các bước

### ⚡ Tăng tốc độ:
- Sử dụng thông tin mặc định
- Không thay đổi URL nếu không cần
- Chuẩn bị sẵn thông tin trước khi bắt đầu

### 🔒 Bảo mật:
- Tool chỉ hoạt động local, không gửi dữ liệu đi đâu
- Thông tin chỉ hiển thị trong log và form
- Đóng tool khi không sử dụng

## 🚨 Xử lý sự cố

### ❌ Chrome không khởi động
**Giải pháp**:
- Đóng tất cả Chrome đang chạy
- Khởi động lại tool
- Kiểm tra Chrome đã cài đặt chưa

### ❌ Website không truy cập được
**Giải pháp**:
- Tool tự động thử nhiều URL
- Kiểm tra kết nối internet
- Thử truy cập thủ công trước

### ❌ Không điền được form
**Giải pháp**:
- Kiểm tra trang có form đăng ký không
- Thử điền thủ công một số trường
- Xem log để biết trường nào bị lỗi

### ❌ Không tìm thấy nút đăng ký
**Giải pháp**:
- Click thủ công nút đăng ký
- Kiểm tra form đã điền đủ chưa
- Scroll xuống tìm nút submit

## 📱 Cài đặt BlueStacks (Tùy chọn)

### 🔧 Để sử dụng tính năng BlueStacks:
1. **Tải BlueStacks**: https://www.bluestacks.com/
2. **Cài đặt** với cài đặt mặc định
3. **Bật ADB Debugging**:
   - Settings → About → Tap 7 lần "Build Number"
   - Settings → Developer Options → Bật "USB Debugging"
4. **Cài Chrome** từ Play Store trong BlueStacks
5. **Cài thư viện**: `pip install psutil`

### 💡 Lưu ý BlueStacks:
- Tính năng này là **tùy chọn**, tool vẫn hoạt động bình thường nếu không dùng
- Xem chi tiết trong file `BLUESTACKS_GUIDE.md`

## 📁 Files

### 🎯 Main files:
- `simple_auto_register.py` - Tool chính
- `run_simple.py` - Script khởi chạy
- `start_simple.bat` - File batch cho Windows
- `bluestacks_automation.py` - Module BlueStacks (mới)

### 📝 Log files:
- `auto_register.log` - Log chi tiết
- Kiểm tra file này khi có lỗi

### 📚 Documentation:
- `SIMPLE_GUIDE.md` - Hướng dẫn chính
- `BLUESTACKS_GUIDE.md` - Hướng dẫn BlueStacks chi tiết

## 🎉 Kết luận

Tool đơn giản này tập trung vào việc tự động đăng ký với giao diện thân thiện và quy trình rõ ràng:

1. **🚀 Khởi động Chrome** → Mở trình duyệt
2. **🌐 Truy cập 13win** → Vào website
3. **🤖 Tự động Đăng ký** → Điền form và submit

**Đơn giản, hiệu quả, dễ sử dụng!** 🎯
