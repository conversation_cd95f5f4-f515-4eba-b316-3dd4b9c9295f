#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 13WIN AUTOMATION - SCRIPT ĐƠN GIẢN
Tự động đăng nhập và nhận thưởng 34k trên 13win.com qua BlueStacks

Cách sử dụng:
1. Mở BlueStacks và đảm bảo app 13win.com đã được cài đặt
2. Chạy script này: python run_13win_automation.py
3. Nhập tài khoản và mật khẩu khi được yêu cầu
4. <PERSON> dõi automation và can thiệp thủ công nếu cần
"""

import sys
import time
from bluestacks_automation import BlueStacksAutomation

def print_banner():
    """In banner chào mừng"""
    print("=" * 60)
    print("🎯 13WIN AUTOMATION - NHẬN THƯỞNG 34K TỰ ĐỘNG")
    print("=" * 60)
    print("📱 Hỗ trợ: BlueStacks Android Emulator")
    print("🎮 Target: 13win.com")
    print("💰 Mục tiêu: Tự động đăng nhập và nhận thưởng")
    print("=" * 60)

def get_user_credentials():
    """Lấy thông tin đăng nhập từ người dùng"""
    print("\n🔐 NHẬP THÔNG TIN ĐĂNG NHẬP:")
    print("-" * 40)
    
    username = input("👤 Tài khoản 13win: ").strip()
    if not username:
        print("❌ Tài khoản không được để trống!")
        return None, None
    
    password = input("🔑 Mật khẩu: ").strip()
    if not password:
        print("❌ Mật khẩu không được để trống!")
        return None, None
    
    print(f"\n✅ Thông tin đã nhập:")
    print(f"   👤 Tài khoản: {username}")
    print(f"   🔑 Mật khẩu: {'*' * len(password)}")
    
    confirm = input("\n❓ Xác nhận thông tin đúng? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', 'đúng', 'ok']:
        return None, None
    
    return username, password

def check_prerequisites():
    """Kiểm tra điều kiện trước khi chạy"""
    print("\n🔍 KIỂM TRA ĐIỀU KIỆN:")
    print("-" * 40)
    
    automation = BlueStacksAutomation()
    
    # Check BlueStacks installation
    if not automation.bluestacks_path:
        print("❌ Không tìm thấy BlueStacks!")
        print("💡 Vui lòng cài đặt BlueStacks trước khi chạy script")
        return False
    
    print(f"✅ BlueStacks: {automation.bluestacks_path}")
    
    # Check ADB
    if not automation.adb_path:
        print("❌ Không tìm thấy ADB!")
        print("💡 ADB là thành phần cần thiết của BlueStacks")
        return False
    
    print(f"✅ ADB: {automation.adb_path}")
    
    # Check if BlueStacks is running
    if not automation.is_bluestacks_running():
        print("⚠️ BlueStacks chưa chạy!")
        print("💡 Vui lòng mở BlueStacks trước khi tiếp tục")
        
        start_choice = input("❓ Bạn có muốn script tự động khởi động BlueStacks? (y/n): ").strip().lower()
        if start_choice in ['y', 'yes', 'có']:
            print("🚀 Đang khởi động BlueStacks...")
            if not automation.start_bluestacks():
                print("❌ Không thể khởi động BlueStacks!")
                return False
        else:
            print("💡 Vui lòng mở BlueStacks thủ công và chạy lại script")
            return False
    
    print("✅ BlueStacks đang chạy")
    
    return True

def run_automation_with_monitoring(username, password):
    """Chạy automation với monitoring"""
    print("\n🤖 BẮT ĐẦU AUTOMATION:")
    print("-" * 40)
    
    automation = BlueStacksAutomation()
    
    print("📋 Các bước sẽ thực hiện:")
    print("   1. Kết nối ADB với BlueStacks")
    print("   2. Mở app 13win.com")
    print("   3. Đăng nhập tài khoản")
    print("   4. Tìm và nhận thưởng 34k")
    print("   5. Hoàn thành")
    
    input("\n⏸️ Nhấn Enter để bắt đầu automation...")
    
    try:
        success = automation.run_automation(username, password)
        
        if success:
            print("\n🎉 AUTOMATION HOÀN THÀNH!")
            print("-" * 40)
            print("✅ Đã thực hiện tất cả các bước automation")
            print("💡 Vui lòng kiểm tra BlueStacks để xác nhận kết quả:")
            print("   - Đã đăng nhập thành công chưa?")
            print("   - Đã nhận được thưởng 34k chưa?")
            print("   - Có cần thực hiện thêm bước nào không?")
        else:
            print("\n⚠️ AUTOMATION KHÔNG HOÀN THÀNH")
            print("-" * 40)
            print("❌ Có lỗi xảy ra trong quá trình automation")
            print("💡 Vui lòng kiểm tra BlueStacks và thực hiện thủ công")
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️ AUTOMATION BỊ DỪNG")
        print("-" * 40)
        print("🛑 Người dùng đã dừng automation (Ctrl+C)")
        print("💡 Bạn có thể tiếp tục thủ công trong BlueStacks")
        return False
    
    except Exception as e:
        print(f"\n❌ LỖI AUTOMATION: {str(e)}")
        print("-" * 40)
        print("💡 Vui lòng thử lại hoặc thực hiện thủ công")
        return False

def main():
    """Hàm chính"""
    try:
        # Print banner
        print_banner()
        
        # Check prerequisites
        if not check_prerequisites():
            print("\n❌ Không đủ điều kiện để chạy automation!")
            print("💡 Vui lòng khắc phục các vấn đề trên và thử lại")
            sys.exit(1)
        
        # Get credentials
        username, password = get_user_credentials()
        if not username or not password:
            print("\n❌ Thông tin đăng nhập không hợp lệ!")
            sys.exit(1)
        
        # Run automation
        success = run_automation_with_monitoring(username, password)
        
        # Final message
        print("\n" + "=" * 60)
        if success:
            print("🎯 AUTOMATION THÀNH CÔNG!")
            print("💰 Hãy kiểm tra tài khoản để xác nhận thưởng đã được nhận")
        else:
            print("⚠️ AUTOMATION CẦN KIỂM TRA")
            print("🔍 Vui lòng xem lại BlueStacks và hoàn thành thủ công nếu cần")
        
        print("📞 Nếu có vấn đề, hãy kiểm tra log chi tiết ở trên")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
