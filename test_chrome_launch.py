#!/usr/bin/env python3
"""
Test script để thử mở Chrome trong BlueStacks bằng cách khác
"""

import subprocess
import time

def test_chrome_launch():
    """Test mở Chrome trong BlueStacks"""
    print("🧪 Testing Chrome Launch in BlueStacks...")
    print("=" * 50)
    
    # Thử các cách khác nhau để mở Chrome
    methods = [
        {
            'name': 'ADB Shell AM Start',
            'cmd': [
                "C:\\Program Files\\BlueStacks_nxt\\HD-Adb.exe",
                "shell", "am", "start", "-n",
                "com.android.chrome/com.google.android.apps.chrome.Main"
            ]
        },
        {
            'name': 'ADB Shell AM Start with URL',
            'cmd': [
                "C:\\Program Files\\BlueStacks_nxt\\HD-Adb.exe", 
                "shell", "am", "start", "-a", "android.intent.action.VIEW",
                "-d", "https://13win16.com/?id=391111507"
            ]
        },
        {
            'name': 'ADB Shell Input Tap (Home)',
            'cmd': [
                "C:\\Program Files\\BlueStacks_nxt\\HD-Adb.exe",
                "shell", "input", "tap", "500", "500"
            ]
        }
    ]
    
    for method in methods:
        print(f"\n🔍 Thử phương pháp: {method['name']}")
        try:
            result = subprocess.run(
                method['cmd'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            print(f"Return code: {result.returncode}")
            if result.stdout:
                print(f"Output: {result.stdout}")
            if result.stderr:
                print(f"Error: {result.stderr}")
                
            if result.returncode == 0:
                print("✅ Thành công!")
            else:
                print("❌ Thất bại!")
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout!")
        except Exception as e:
            print(f"❌ Lỗi: {str(e)}")
        
        time.sleep(2)
    
    print("\n🎯 Test hoàn thành!")
    print("💡 Nếu tất cả đều thất bại, có thể ADB debugging chưa bật")

if __name__ == "__main__":
    test_chrome_launch()
