# 🎯 13WIN AUTOMATION - HƯỚNG DẪN SỬ DỤNG

## 📋 Mô tả
Script tự động đăng nhập và nhận thưởng 34k trên 13win.com thông qua BlueStacks Android Emulator.

## 🔧 Yêu cầu hệ thống
- **Windows 10/11**
- **BlueStacks** (đã cài đặt và cấu hình)
- **Python 3.7+** (đã cài đặt)
- **App 13win.com** (đã cài đặt trong BlueStacks)

## 📦 Cài đặt

### 1. Cài đặt BlueStacks
- Tải và cài đặt BlueStacks từ trang chính thức
- Khởi động BlueStacks và hoàn thành setup
- Cài đặt app 13win.com từ Google Play Store hoặc APK

### 2. Cài đặt Python dependencies
```bash
pip install psutil
```

### 3. Kiểm tra cấu hình
- Mở BlueStacks và đảm bảo nó chạy ổn định
- Kiểm tra app 13win.com có thể mở được
- <PERSON><PERSON><PERSON> bảo có kết nối internet

## 🚀 Cách sử dụng

### Phương pháp 1: Chạy bằng file .bat (Đơn giản)
1. **Double-click** vào file `run_automation.bat`
2. Làm theo hướng dẫn trên màn hình
3. Nhập tài khoản và mật khẩu khi được yêu cầu
4. Theo dõi automation và can thiệp nếu cần

### Phương pháp 2: Chạy bằng Python (Nâng cao)
```bash
python run_13win_automation.py
```

## 📱 Quy trình automation

### Bước 1: Kiểm tra điều kiện
- ✅ Phát hiện BlueStacks
- ✅ Kiểm tra ADB connection
- ✅ Xác nhận BlueStacks đang chạy

### Bước 2: Nhập thông tin
- 👤 Nhập tài khoản 13win
- 🔑 Nhập mật khẩu
- ✅ Xác nhận thông tin

### Bước 3: Thực hiện automation
- 🔗 Kết nối ADB với BlueStacks
- 📱 Mở app 13win.com (tọa độ: 411, 215)
- ⏳ Chờ app load hoàn toàn
- 🔐 Tự động đăng nhập
- 🎁 Tìm và click nút nhận thưởng 34k
- ✅ Hoàn thành

## 🎯 Tọa độ automation

### App 13win.com
- **Vị trí**: (411, 215)
- **Mô tả**: Icon app trên màn hình chính BlueStacks

### Form đăng nhập
- **Username field**: (400, 300)
- **Password field**: (400, 380)
- **Login button**: (400, 460)

### Nút nhận thưởng
- **Vị trí 1**: (400, 600) - Center area
- **Vị trí 2**: (400, 700) - Lower center
- **Vị trí 3**: (500, 650) - Right center
- **Vị trí 4**: (300, 650) - Left center
- **Vị trí 5**: (400, 800) - Bottom area

## ⚠️ Lưu ý quan trọng

### Trước khi chạy
1. **Đóng tất cả ứng dụng không cần thiết** để tránh xung đột
2. **Không di chuyển chuột** trong quá trình automation
3. **Đảm bảo BlueStacks ở chế độ fullscreen** hoặc kích thước cố định
4. **Kiểm tra kết nối internet** ổn định

### Trong quá trình chạy
1. **Theo dõi màn hình** để can thiệp khi cần
2. **Đọc log messages** để hiểu automation đang làm gì
3. **Sẵn sàng thực hiện thủ công** nếu automation thất bại
4. **Không tắt BlueStacks** cho đến khi hoàn thành

### Sau khi chạy
1. **Kiểm tra kết quả** trong BlueStacks
2. **Xác nhận đăng nhập thành công**
3. **Kiểm tra thưởng đã được nhận**
4. **Lưu lại log** nếu có vấn đề

## 🔧 Troubleshooting

### Lỗi thường gặp

#### "Không tìm thấy BlueStacks"
- **Nguyên nhân**: BlueStacks chưa được cài đặt hoặc cài ở vị trí khác
- **Giải pháp**: Cài đặt BlueStacks hoặc cập nhật đường dẫn trong config

#### "ADB connection failed"
- **Nguyên nhân**: BlueStacks chưa khởi động hoàn toàn hoặc ADB bị lỗi
- **Giải pháp**: Restart BlueStacks và chạy lại script

#### "No devices/emulators found"
- **Nguyên nhân**: BlueStacks instance chưa sẵn sàng
- **Giải pháp**: Đợi BlueStacks load hoàn toàn và thử lại

#### "App không mở được"
- **Nguyên nhân**: Tọa độ app không chính xác hoặc app chưa cài đặt
- **Giải pháp**: Kiểm tra app đã cài đặt và điều chỉnh tọa độ

### Điều chỉnh tọa độ
Nếu automation click sai vị trí, bạn có thể:
1. Mở file `bluestacks_automation.py`
2. Tìm dòng `app_13win_position = (411, 215)`
3. Thay đổi tọa độ phù hợp với màn hình của bạn
4. Lưu file và chạy lại

## 📞 Hỗ trợ

### Log files
- Automation sẽ in log chi tiết ra console
- Copy log để debug nếu có vấn đề

### Manual fallback
- Nếu automation thất bại, script sẽ hướng dẫn thực hiện thủ công
- Theo dõi hướng dẫn và hoàn thành các bước còn lại

### Cấu hình nâng cao
- Chỉnh sửa file `bluestacks_automation.py` để tùy chỉnh
- Thay đổi timeout, tọa độ, URL theo nhu cầu

## 🎉 Thành công!
Khi automation hoàn thành thành công:
- ✅ Tài khoản đã đăng nhập
- 💰 Thưởng 34k đã được nhận
- 📱 BlueStacks hiển thị kết quả

**Chúc bạn thành công với automation! 🎯**
