#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 FIX BLUESTACKS ADB - Khắc phục vấn đề ADB với BlueStacks

Script này sẽ:
1. Restart ADB server
2. Hướng dẫn bật ADB debugging trong BlueStacks
3. Test kết nối
4. Đ<PERSON>a ra giải pháp khắc phục
"""

import subprocess
import time
import sys

def restart_adb():
    """Restart ADB server"""
    print("🔄 RESTART ADB SERVER")
    print("-" * 30)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    try:
        # Kill server
        print("🛑 Dừng ADB server...")
        subprocess.run([adb_path, "kill-server"], capture_output=True, timeout=10)
        time.sleep(2)
        
        # Start server
        print("🚀 Khởi động ADB server...")
        result = subprocess.run([adb_path, "start-server"], capture_output=True, text=True, timeout=10)
        
        if "daemon started successfully" in result.stdout:
            print("✅ ADB server đã khởi động thành công")
        else:
            print("⚠️ ADB server có thể đã chạy")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi restart ADB: {str(e)}")
        return False

def check_devices():
    """Kiểm tra devices"""
    print("\n🔍 KIỂM TRA DEVICES")
    print("-" * 30)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    try:
        result = subprocess.run([adb_path, "devices"], capture_output=True, text=True, timeout=10)
        
        print("Devices output:")
        print(result.stdout)
        
        if "emulator-5554" in result.stdout and "device" in result.stdout:
            print("✅ Tìm thấy emulator-5554")
            return True
        else:
            print("❌ Không tìm thấy device hoặc device offline")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi kiểm tra devices: {str(e)}")
        return False

def test_shell_connection():
    """Test shell connection"""
    print("\n🧪 TEST SHELL CONNECTION")
    print("-" * 30)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    try:
        # Test simple command
        result = subprocess.run([
            adb_path, "-s", "emulator-5554", "shell", "echo", "test"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "test" in result.stdout:
            print("✅ Shell connection hoạt động!")
            return True
        else:
            print(f"❌ Shell connection thất bại: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test shell: {str(e)}")
        return False

def test_input_commands():
    """Test input commands"""
    print("\n👆 TEST INPUT COMMANDS")
    print("-" * 30)
    
    adb_path = r"C:\Program Files\BlueStacks_nxt\HD-Adb.exe"
    
    try:
        # Test tap command
        print("Testing tap command...")
        tap_result = subprocess.run([
            adb_path, "-s", "emulator-5554", "shell", "input", "tap", "400", "400"
        ], capture_output=True, text=True, timeout=10)
        
        if tap_result.returncode == 0:
            print("✅ Tap command hoạt động!")
            return True
        else:
            print(f"❌ Tap command thất bại: {tap_result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test input: {str(e)}")
        return False

def show_troubleshooting_guide():
    """Hiển thị hướng dẫn khắc phục"""
    print("\n🔧 HƯỚNG DẪN KHẮC PHỤC")
    print("=" * 50)
    
    print("\n📋 Nếu ADB không hoạt động, hãy thử:")
    print("1. 🔄 Restart BlueStacks hoàn toàn:")
    print("   - Đóng BlueStacks")
    print("   - Mở Task Manager và kill tất cả process BlueStacks")
    print("   - Mở lại BlueStacks")
    
    print("\n2. 🛠️ Bật ADB Debugging trong BlueStacks:")
    print("   - Mở BlueStacks")
    print("   - Vào Settings > Advanced")
    print("   - Bật 'Android Debug Bridge (ADB)'")
    print("   - Restart BlueStacks")
    
    print("\n3. 🔧 Kiểm tra cấu hình BlueStacks:")
    print("   - Đảm bảo BlueStacks chạy instance Pie64")
    print("   - Kiểm tra BlueStacks không bị antivirus block")
    print("   - Thử chạy BlueStacks với quyền Administrator")
    
    print("\n4. 🌐 Thử phương pháp thay thế:")
    print("   - Sử dụng BlueStacks Macro Recorder")
    print("   - Sử dụng mouse automation thay vì ADB")
    print("   - Thử emulator khác như NoxPlayer")

def create_alternative_automation():
    """Tạo automation thay thế không dùng ADB"""
    print("\n🎯 TẠO AUTOMATION THAY THẾ")
    print("-" * 30)
    
    print("💡 Nếu ADB không hoạt động, tôi có thể tạo automation dùng:")
    print("   - Mouse clicks trực tiếp")
    print("   - Keyboard input")
    print("   - Screen coordinates")
    
    choice = input("\n❓ Bạn có muốn tôi tạo automation thay thế? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes', 'có']:
        print("🚀 Sẽ tạo automation thay thế...")
        return True
    
    return False

def main():
    """Hàm chính"""
    try:
        print("🔧 KHẮC PHỤC BLUESTACKS ADB")
        print("=" * 50)
        
        # Step 1: Restart ADB
        if not restart_adb():
            print("❌ Không thể restart ADB!")
            show_troubleshooting_guide()
            return
        
        # Step 2: Check devices
        if not check_devices():
            print("❌ Không tìm thấy devices!")
            show_troubleshooting_guide()
            return
        
        # Step 3: Test shell
        if not test_shell_connection():
            print("❌ Shell connection không hoạt động!")
            show_troubleshooting_guide()
            
            # Offer alternative
            if create_alternative_automation():
                print("✅ Sẽ tạo automation thay thế!")
            return
        
        # Step 4: Test input
        if not test_input_commands():
            print("❌ Input commands không hoạt động!")
            show_troubleshooting_guide()
            return
        
        # Success!
        print("\n🎉 TẤT CẢ HOẠT ĐỘNG TỐTL!")
        print("=" * 50)
        print("✅ ADB server: OK")
        print("✅ Device connection: OK") 
        print("✅ Shell commands: OK")
        print("✅ Input commands: OK")
        print("\n💡 Automation sẽ hoạt động bình thường!")
        print("🚀 Bạn có thể chạy: python run_13win_automation.py")
        
    except KeyboardInterrupt:
        print("\n\n👋 Khắc phục bị dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {str(e)}")

if __name__ == "__main__":
    main()
